// Theme types aligned with design system color structure
// Based on design system from repomix-output.xml and updated SCSS tokens

export type ColorScheme = 'light' | 'dark';

// Design System Color Categories
export interface DesignSystemColors {
  // Background Colors (theme.colors.background*)
  backgroundPrimary: string;      // Primary background (white/dark surface)
  backgroundSecondary: string;    // Secondary background
  backgroundTertiary: string;     // Tertiary background
  backgroundScrim: string;        // Overlay/scrim background
  backgroundDark: string;         // Dark background
  backgroundPressed: string;      // Pressed state background

  // Text Colors (theme.colors.text*, tokens.colors.textColors)
  textPrimary: string;            // Primary text color
  textSecondary: string;          // Secondary text color
  textMuted: string;              // Muted text color
  textBrand: string;              // Brand text color
  textOnPrimary: string;          // Text on primary background
  textOnBackgroundVarFive: string; // Text on background variant 5

  // Brand Colors (theme.colors.primary/secondary)
  primary: string;                // Primary brand color
  secondary: string;              // Secondary brand color
  primaryHover: string;           // Primary hover state
  primaryLight: string;           // Light primary variant

  // Feedback Colors
  success: string;                // Success state
  error: string;                  // Error state
  warning: string;                // Warning state
  info: string;                   // Info state

  // Feedback Background Colors (theme.colors.feedbackBackground*)
  feedbackBackgroundSuccess: string;  // Success feedback background
  feedbackBackgroundError: string;    // Error feedback background
  feedbackBackgroundWarning: string;  // Warning feedback background
  feedbackBackgroundInfo: string;     // Info feedback background

  // Neutral Colors (tokens.colors.neutralColors)
  neutralWhite: string;           // White
  neutral50: string;              // Almost white
  neutral100: string;             // Very light gray
  neutral200: string;             // Light gray
  neutral300: string;             // Medium light gray
  neutral400: string;             // Medium gray
  neutral500: string;             // Base gray
  neutral600: string;             // Medium dark gray
  neutral700: string;             // Dark gray
  neutral800: string;             // Very dark gray
  neutral900: string;             // Almost black

  // Color Variants (theme.colors.*)
  green100: string;               // Light green
  green900: string;               // Dark green
  yellow100: string;              // Light yellow/orange
  yellow900: string;              // Dark yellow/orange
  pink100: string;                // Light pink/red
  pink900: string;                // Dark pink/red

  // Link Colors (tokens.colors.linkColors)
  linkPrimary: string;            // Primary link color
  linkBrand: string;              // Brand link color

  // Border Colors
  border: string;                 // Default border color
  borderHover: string;            // Hover state border color

  // Surface Colors (legacy compatibility)
  surface: string;                // Primary surface
  surfaceSecondary: string;       // Secondary surface
}

// Chat-specific color interface
export interface ChatColors {
  userBackground: string;         // User message background
  agentBackground: string;        // Agent message background
  userText: string;               // User message text
  agentText: string;              // Agent message text
  headerBackground: string;       // Chat header background
  accent: string;                 // Chat accent color
}

// Status color interface
export interface StatusColors {
  connected: string;              // Connected state
  connecting: string;             // Connecting state
  disconnected: string;           // Disconnected state
  error: string;                  // Error state
}

// Complete theme interface
export interface Theme {
  colors: DesignSystemColors;
  chat: ChatColors;
  status: StatusColors;
  scheme: ColorScheme;
}

// Theme configuration interface
export interface ThemeConfig {
  light: Theme;
  dark: Theme;
}

// CSS Custom Property names (for type safety)
export const CSS_CUSTOM_PROPERTIES = {
  // Background Colors
  BACKGROUND: '--color-background',
  BACKGROUND_PRIMARY: '--color-background-primary',
  BACKGROUND_SECONDARY: '--color-background-secondary',
  BACKGROUND_TERTIARY: '--color-background-tertiary',
  BACKGROUND_SCRIM: '--color-background-scrim',
  BACKGROUND_DARK: '--color-background-dark',
  BACKGROUND_PRESSED: '--color-background-pressed',

  // Surface Colors
  SURFACE: '--color-surface',
  SURFACE_SECONDARY: '--color-surface-secondary',

  // Text Colors
  TEXT_PRIMARY: '--color-text-primary',
  TEXT_SECONDARY: '--color-text-secondary',
  TEXT_MUTED: '--color-text-muted',
  TEXT_BRAND: '--color-text-brand',
  TEXT_ON_PRIMARY: '--color-text-on-primary',

  // Brand Colors
  PRIMARY: '--color-primary',
  PRIMARY_HOVER: '--color-primary-hover',
  PRIMARY_LIGHT: '--color-primary-light',
  SECONDARY: '--color-secondary',

  // Feedback Colors
  SUCCESS: '--color-success',
  ERROR: '--color-error',
  WARNING: '--color-warning',
  INFO: '--color-info',

  // Feedback Background Colors
  FEEDBACK_BG_SUCCESS: '--color-feedback-bg-success',
  FEEDBACK_BG_ERROR: '--color-feedback-bg-error',
  FEEDBACK_BG_WARNING: '--color-feedback-bg-warning',
  FEEDBACK_BG_INFO: '--color-feedback-bg-info',

  // Link Colors
  LINK_PRIMARY: '--color-link-primary',
  LINK_BRAND: '--color-link-brand',

  // Border Colors
  BORDER: '--color-border',
  BORDER_HOVER: '--color-border-hover',

  // Chat Colors
  CHAT_USER_BG: '--color-chat-user-bg',
  CHAT_AGENT_BG: '--color-chat-agent-bg',
  CHAT_USER_TEXT: '--color-chat-user-text',
  CHAT_AGENT_TEXT: '--color-chat-agent-text',
  CHAT_HEADER_BG: '--color-chat-header-bg',
  CHAT_ACCENT: '--color-chat-accent',

  // Status Colors
  STATUS_CONNECTED: '--color-status-connected',
  STATUS_CONNECTING: '--color-status-connecting',
  STATUS_DISCONNECTED: '--color-status-disconnected',
  STATUS_ERROR: '--color-status-error',

  // Legacy Eneco Brand Colors
  ENECO_PURPLE: '--color-eneco-purple',
  ENECO_GREEN: '--color-eneco-green',
  ENECO_ORANGE: '--color-eneco-orange',
} as const;

// Type for CSS custom property names
export type CSSCustomProperty = typeof CSS_CUSTOM_PROPERTIES[keyof typeof CSS_CUSTOM_PROPERTIES];

// Helper type for theme-aware component props
export interface ThemeAwareProps {
  theme?: Partial<DesignSystemColors>;
  colorScheme?: ColorScheme;
}

// Color variant types for components
export type ColorVariant = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info' 
  | 'neutral';

// Background variant types
export type BackgroundVariant = 
  | 'primary' 
  | 'secondary' 
  | 'tertiary' 
  | 'scrim' 
  | 'dark' 
  | 'pressed';

// Text variant types
export type TextVariant = 
  | 'primary' 
  | 'secondary' 
  | 'muted' 
  | 'brand' 
  | 'onPrimary';

// Design system token structure (for compatibility with repomix design system)
export interface DesignSystemTokens {
  colors: {
    neutralColors: {
      neutralWhite: string;
      neutral300: string;
    };
    secondaryColors: {
      accentGreen700: string;
    };
    backgroundColors: {
      backgroundPrimary: string;
      backgroundTertiary: string;
    };
    textColors: {
      textPrimary: string;
      textBrand: string;
    };
    linkColors: {
      linkPrimary: string;
      linkBrand: string;
    };
  };
}

// Theme context type (for React context)
export interface ThemeContextType {
  theme: Theme;
  colorScheme: ColorScheme;
  toggleTheme: () => void;
  setTheme: (scheme: ColorScheme) => void;
}
