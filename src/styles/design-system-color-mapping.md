# Design System Color Mapping Analysis

## Current Chatbot Color System (SCSS-based)

### Primary Colors (Eneco Purple)
- `$primary-50: #f5f3ff` → Light purple tint
- `$primary-100: #ede9fe` → Very light purple
- `$primary-200: #ddd6fe` → Light purple
- `$primary-300: #c4b5fd` → Medium light purple
- `$primary-400: #a78bfa` → Medium purple
- `$primary-500: #8b5cf6` → Base purple
- `$primary-600: #7c3aed` → Medium dark purple
- `$primary-700: #6d28d9` → Dark purple (Eneco Primary)
- `$primary-800: #5b21b6` → Very dark purple
- `$primary-900: #4c1d95` → Darkest purple

### Gray/Neutral Colors
- `$gray-50: #f9fafb` → Almost white
- `$gray-100: #f3f4f6` → Very light gray
- `$gray-200: #e5e7eb` → Light gray
- `$gray-300: #d1d5db` → Medium light gray
- `$gray-400: #9ca3af` → Medium gray
- `$gray-500: #6b7280` → Base gray
- `$gray-600: #4b5563` → Medium dark gray
- `$gray-700: #374151` → Dark gray
- `$gray-800: #1f2937` → Very dark gray
- `$gray-900: #111827` → Almost black

### Success Colors (Eneco Green)
- `$success-500: #22c55e` → Eneco Green

### Warning Colors (Eneco Orange)
- `$warning-500: #f97316` → Eneco Orange

### Error Colors
- `$error-500: #ef4444` → Error Red

## Design System Color Structure (from repomix-output.xml)

### Theme Colors (theme.colors.*)
- `backgroundPrimary` → Primary background
- `backgroundSecondary` → Secondary background
- `backgroundTertiary` → Tertiary background
- `backgroundScrim` → Overlay/scrim background
- `backgroundDark` → Dark background
- `backgroundPressed` → Pressed state background
- `feedbackBackgroundSuccess` → Success feedback background
- `feedbackBackgroundError` → Error feedback background
- `feedbackBackgroundWarning` → Warning feedback background
- `feedbackBackgroundInfo` → Info feedback background
- `textPrimary` → Primary text color
- `textBrand` → Brand text color
- `onPrimary` → Text on primary background
- `textOnBackgroundVarFive` → Text on background variant 5
- `primary` → Primary brand color
- `secondary` → Secondary brand color
- `background` → Base background color
- `green100`, `green900` → Green color variants
- `yellow100`, `yellow900` → Yellow color variants
- `pink100`, `pink900` → Pink color variants
- `neutral900` → Dark neutral
- `neutralWhite` → White

### Token Colors (tokens.colors.*)
- `neutralColors.neutralWhite` → White
- `neutralColors.neutral300` → Light neutral
- `secondaryColors.accentGreen700` → Accent green
- `backgroundColors.backgroundPrimary` → Primary background
- `backgroundColors.backgroundTertiary` → Tertiary background
- `textColors.textPrimary` → Primary text
- `textColors.textBrand` → Brand text
- `linkColors.linkPrimary` → Primary link color
- `linkColors.linkBrand` → Brand link color

## Proposed Color Mapping Strategy

### 1. Map Design System Colors to Current SCSS Variables

**Background Colors:**
- `theme.colors.backgroundPrimary` → `$eneco-white` (#ffffff)
- `theme.colors.backgroundSecondary` → `$gray-100` (#f3f4f6)
- `theme.colors.backgroundTertiary` → `$gray-50` (#f9fafb)
- `theme.colors.background` → `$eneco-light-gray` (#f8fafc)

**Text Colors:**
- `theme.colors.textPrimary` → `$gray-900` (#111827)
- `theme.colors.textBrand` → `$primary-700` (#6d28d9)
- `theme.colors.onPrimary` → `$eneco-white` (#ffffff)

**Brand Colors:**
- `theme.colors.primary` → `$primary-700` (#6d28d9)
- `theme.colors.secondary` → `$primary-600` (#7c3aed)
- `tokens.colors.secondaryColors.accentGreen700` → `$success-600` (#16a34a)

**Neutral Colors:**
- `theme.colors.neutralWhite` → `$eneco-white` (#ffffff)
- `theme.colors.neutral900` → `$gray-900` (#111827)
- `tokens.colors.neutralColors.neutral300` → `$gray-300` (#d1d5db)

**Feedback Colors:**
- `theme.colors.green100` → `$success-100` (#dcfce7)
- `theme.colors.green900` → `$success-900` (#14532d)
- `theme.colors.yellow100` → `$warning-100` (#ffedd5)
- `theme.colors.yellow900` → `$warning-900` (#7c2d12)
- `theme.colors.pink100` → `$error-100` (#fee2e2)
- `theme.colors.pink900` → `$error-900` (#7f1d1d)

### 2. Chat-Specific Color Applications

**Message Bubbles:**
- User messages: `theme.colors.primary` → `$chat-user-bg` (#7c3aed)
- Agent messages: `theme.colors.backgroundTertiary` → `$chat-agent-bg` (#f9fafb)
- User text: `theme.colors.onPrimary` → `$chat-user-text` (white)
- Agent text: `theme.colors.textPrimary` → `$chat-agent-text` (#111827)

**Interactive Elements:**
- Buttons: `theme.colors.primary` → `$primary-700` (#6d28d9)
- Links: `tokens.colors.linkColors.linkBrand` → `$primary-700` (#6d28d9)
- Borders: `theme.colors.backgroundSecondary` → `$gray-200` (#e5e7eb)

### 3. Implementation Strategy

1. **Update SCSS Variables**: Map design system colors to existing SCSS variables
2. **Create New Variables**: Add missing design system colors as new SCSS variables
3. **Update CSS Custom Properties**: Ensure CSS custom properties reflect the new color mappings
4. **Component Updates**: Update component styles to use the mapped colors
5. **Theme Consistency**: Ensure both light and dark themes use the design system colors

This mapping maintains the existing Eneco brand identity while incorporating the design system's color structure and naming conventions.
