// Message Input Component Styles

.message-input {
  @include padding(4);
  border-top: 1px solid var(--color-border);
  // Use design system background color
  background: var(--color-background-primary);
  @include transition();
  
  &--expanded {
    .message-input__field {
      min-height: 60px;
    }
  }
  
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &--error {
    .message-input__field-wrapper {
      border-color: var(--color-error);
    }
  }
}

.message-input__container {
  max-width: 100%;
}

.message-input__field-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-2);
  @include padding(3);
  border: 2px solid var(--color-border);
  @include rounded(lg);
  // Use design system background color
  background: var(--color-background-primary);
  @include transition();
  @include shadow(sm);

  &:focus-within {
    // Use design system primary color for focus state
    border-color: var(--color-primary);
    @include shadow(md);
    background: var(--color-background-primary);
  }

  &:hover:not(:focus-within) {
    border-color: var(--color-border-hover);
  }
}

.message-input__field {
  flex: 1;
  border: none;
  background: none;
  resize: none;
  outline: none;
  @include text-base;
  color: var(--color-text-primary);
  line-height: 1.5;
  min-height: 40px;
  max-height: 120px;
  
  &::placeholder {
    color: var(--color-text-muted);
  }
  
  &:disabled {
    cursor: not-allowed;
  }
}

.message-input__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  flex-shrink: 0;
}

.message-input__action {
  @include padding(2);
  border: none;
  background: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  @include rounded(md);
  @include transition();
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover:not(:disabled) {
    background: var(--color-background-secondary);
    color: var(--color-text-primary);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

.message-input__send {
  @include padding(3);
  border: none;
  background: var(--color-background-secondary);
  color: var(--color-text-muted);
  cursor: pointer;
  @include rounded(lg);
  @include transition();
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;

  &--active {
    background: linear-gradient(135deg, var(--color-eneco-red) 0%, var(--color-secondary) 100%);
    color: var(--color-text-on-primary);
    @include shadow(sm);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-eneco-red) 100%);
      transform: scale(1.05);
      @include shadow(md);
    }

    &:active {
      transform: scale(0.95);
    }
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

.message-input__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-2);
  min-height: 20px;
}

.message-input__error {
  @include text-xs;
  color: var(--color-error);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.message-input__character-count {
  @include text-xs;
  color: var(--color-text-muted);
  margin-left: auto;
  
  &--warning {
    color: var(--color-warning);
  }
  
  &--error {
    color: var(--color-error);
    font-weight: var(--font-weight-medium);
  }
}

.message-input__hint {
  @include text-xs;
  color: var(--color-text-muted);
  font-style: italic;
}

// File upload area (for future use)
.message-input__upload-area {
  border: 2px dashed var(--color-border);
  @include rounded(lg);
  @include padding(6);
  text-align: center;
  @include transition();
  
  &--dragover {
    border-color: var(--color-primary);
    background: var(--color-primary-light);
  }
}

.message-input__upload-text {
  @include text-sm;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2);
}

.message-input__upload-button {
  @extend .button;
  @extend .button--secondary;
  @extend .button--sm;
}

// Emoji picker (for future use)
.message-input__emoji-picker {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: var(--spacing-2);
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  @include rounded(lg);
  @include shadow(lg);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

// Responsive adjustments
@include mobile {
  .message-input {
    @include padding(3);
  }
  
  .message-input__field-wrapper {
    @include padding(2);
  }
  
  .message-input__actions {
    gap: var(--spacing-1);
  }
  
  .message-input__action,
  .message-input__send {
    @include padding(2);
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
  
  .message-input__footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }
  
  .message-input__character-count {
    margin-left: 0;
    align-self: flex-end;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .message-input__field-wrapper {
    border-width: 2px;
    
    &:focus-within {
      border-width: 3px;
    }
  }
  
  .message-input__send--active {
    border: 2px solid var(--color-text-primary);
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .message-input__send--active {
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: none;
    }
  }
}
