This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: *.ts, *.json
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
animation.ts
borders.ts
colors.ts
index.ts
shadows.ts
spacing.ts
typography.ts
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="animation.ts">
/**
 * Animation tokens
 */

// Transition durations
export const durations = {
  quick: 200,
  medium: 300,
  slow: 500,
};

// Easing functions
export const easings = {
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  linear: 'linear',
};

// Transitions
export const transitions = {
  easeQuick: {
    duration: durations.quick,
    easing: easings.easeInOut,
  },
  easeMedium: {
    duration: durations.medium,
    easing: easings.easeInOut,
  },
  easeSlow: {
    duration: durations.slow,
    easing: easings.easeInOut,
  },
};

// Animation presets
export const animations = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  fadeOut: {
    from: { opacity: 1 },
    to: { opacity: 0 },
  },
  slideInUp: {
    from: { translateY: 100 },
    to: { translateY: 0 },
  },
  slideOutDown: {
    from: { translateY: 0 },
    to: { translateY: 100 },
  },
};
</file>

<file path="borders.ts">
/**
 * Border tokens
 */

import { borderColors } from './colors';
import { GRID } from './spacing';

// Border widths
export const borderWidths = {
  none: 0,
  s: 1,
  m: 2,
  l: 3,
};

// Border radii
export const radii = {
  none: 0,
  xs: GRID,
  s: GRID * 2,
  m: GRID * 4,
  l: GRID * 6,
  xl: GRID * 8,
  round: 9999,
};

// Outlines for focus states
export const outlines = {
  outlineFocus: {
    width: borderWidths.m,
    color: borderColors.borderFocus,
  },
  outlineInputFocus: {
    width: borderWidths.s,
    color: borderColors.borderFocus,
  },
};

// Component-specific border tokens
export const componentBorders = {
  button: {
    borderRadiusDefault: radii.s,
    borderRadiusRebrand: radii.m,
    borderWidthSecondary: borderWidths.m,
    focusOutlineWidth: borderWidths.s,
  },
  badge: {
    borderRadius: radii.s,
  },
  card: {
    borderRadius: radii.m,
  },
  input: {
    borderRadius: radii.s,
    borderWidth: borderWidths.s,
  },
};
</file>

<file path="colors.ts">
/**
 * Color tokens
 */

// Base color palette
export const brandColors = {
  brandRed: "#E5384C",
  brandOrange: "#EA714F",
  brandDarkRed: "#D21242",
  brandLightRed: "#F9C7CC",
};

export const brandGradients = {
  brandGradientStart: brandColors.brandRed,
  brandGradientEnd: brandColors.brandOrange,
  brandGradientDark: "linear-gradient(90deg, #B4334E 0%, #B65A4E 100%)",
};

export const brandSupport = {
  darkerRed: "#A63B3C",
  darkPurple: "#300C38",
};

export const neutralColors = {
  neutralWhite: "#FFF",
  neutral50: "#FCFAFA",
  neutral100: "#F8F6F6",
  neutral300: "#F3F0F0",
  neutral400: "#DFDCDC",
  neutral800: "#716A6A",
  neutral900: "#2F2D2D",
  neutralBlack: "#000",
};

// Opacity colors
export const opacityColors = {
  whiteOpacity15: `${neutralColors.neutralWhite}15`,
  whiteOpacity30: `${neutralColors.neutralWhite}30`,
  blackOpacity40: "#00000040",
  blackOpacity70: `${neutralColors.neutral900}70`,
};

// Secondary colors
export const secondaryColors = {
  // Purple
  purple50: "#F4F3FA",
  purple100: "#E8E6F4",
  purple300: "#C3C6E5",
  purple500: "#8D8CC6",
  purple700: "#8586CF",
  purple800: "#655790",
  purple900: "#3E235B",

  // Pink
  pink50: "#FDEDF1",
  pink100: "#FBDBE3",
  pink300: "#F6B1C6",
  pink500: "#EC6C87",
  pink700: "#C44B6B",
  pink800: "#A04967",
  pink900: "#552748",

  // Blue
  blue50: "#F0FAF8",
  blue100: "#E1F4F1",
  blue300: "#B1DDDF",
  blue500: "#72BDCE",
  blue700: "#3E798D",
  blue900: "#09354B",

  // Bluegray
  blueGray50: "#EBF6F0",
  blueGray100: "#D7EDE1",
  blueGray300: "#BDE0D7",
  blueGray500: "#85BAB0",
  blueGray700: "#557C77",
  blueGray900: "#243D3D",

  // Green
  green50: "#F2F7EC",
  green100: "#E4EFD8",
  green300: "#CDE3BB",
  green500: "#7EC389",
  green700: "#009b65",
  green800: "#2C6F49",
  green900: "#0A4033",

  // Accentgreen
  accentGreen100: "#e3faea",
  accentGreen200: "#c0eaca",
  accentGreen300: "#84dc99",
  accentGreen600: "#009b65",
  accentGreen700: "#007250",
  accentGreen800: "#00593f",

  // Yellow
  yellow50: "#FFFAF0",
  yellow100: "#FEF4E0",
  yellow300: "#FDE8B6",
  yellow500: "#FCCA6D",
  yellow700: "#CE7731",
  yellow900: "#501318",

  // Eneco red
  enecoRed600: "#e5384c",
  enecoRed700: "#d21242",
  enecoRed800: "#bf0639",
  enecoRed900: "#821034",

  // Orange
  orange100: "#ffe7dc",
  orange300: "#ffba8f",
  orange400: "#ff9363",
  orange500: "#ea714f",
};

// Semantic color tokens
export const backgroundColors = {
  backgroundPrimary: neutralColors.neutralWhite,
  backgroundSecondary: neutralColors.neutral100,
  backgroundTertiary: neutralColors.neutral300,
  backgroundScrim: opacityColors.blackOpacity70,
  backgroundDark: neutralColors.neutral900,
  backgroundPressed: neutralColors.neutral100,
};

export const backgroundColoredColors = {
  backgroundVarOne: secondaryColors.purple100,
  backgroundVarTwo: secondaryColors.pink100,
  backgroundVarThree: secondaryColors.blue100,
  backgroundVarFour: secondaryColors.blueGray100,
  backgroundVarFive: secondaryColors.green100,
  backgroundVarSix: secondaryColors.yellow100,
};

export const textColors = {
  textPrimary: neutralColors.neutral900,
  textInverted: neutralColors.neutralWhite,
  textBrand: secondaryColors.accentGreen700,
  textOnBackgroundVarOne: secondaryColors.purple900,
  textOnBackgroundVarTwo: secondaryColors.pink900,
  textOnBackgroundVarThree: secondaryColors.blue900,
  textOnBackgroundVarFour: secondaryColors.blueGray900,
  textOnBackgroundVarFive: secondaryColors.green900,
  textOnBackgroundVarSix: secondaryColors.yellow900,
  textLowEmphasis: neutralColors.neutral800,
};

export const iconColors = {
  iconPrimary: neutralColors.neutral900,
  iconSecondary: neutralColors.neutral800,
  iconTertiary: neutralColors.neutral900,
  iconInverted: neutralColors.neutralWhite,
  iconBrand: brandColors.brandDarkRed,
  iconCooling: secondaryColors.blue700,
  iconElectricity: secondaryColors.green700,
  iconGas: secondaryColors.purple700,
  iconHeat: secondaryColors.pink700,
  iconReview: secondaryColors.accentGreen600,
  iconSolar: secondaryColors.yellow700,
  iconTotal: secondaryColors.blueGray700,
  iconWater: secondaryColors.blue700,
};

export const borderColors = {
  borderDividerLowEmphasis: neutralColors.neutral300,
  borderDividerMediumEmphasis: neutralColors.neutral400,
  borderDividerHighEmphasis: neutralColors.neutral900,
  borderFocus: neutralColors.neutral900,
  borderSelected: secondaryColors.green500,
  outlineHover: neutralColors.neutral300,
};

export const linkColors = {
  linkBrand: textColors.textBrand,
  linkPrimary: textColors.textPrimary,
  linkSecondary: textColors.textLowEmphasis,
  linkDisabled: neutralColors.neutral300,
  linkInverted: textColors.textInverted,
};

export const feedbackColors = {
  feedbackError: brandColors.brandDarkRed,
  feedbackSuccess: secondaryColors.green700,
  feedbackWarning: secondaryColors.yellow700,
  feedbackInfo: secondaryColors.blue700,
  feedbackBackgroundError: neutralColors.neutral100,
  feedbackBackgroundSuccess: secondaryColors.green100,
  feedbackBackgroundWarning: secondaryColors.yellow100,
  feedbackBackgroundInfo: secondaryColors.blue100,
};

export const formColors = {
  formBorderDefault: neutralColors.neutral800,
  formBorderError: brandColors.brandDarkRed,
  formBorderHover: neutralColors.neutral900,
  formErrorMessageBackground: brandSupport.darkerRed,
  formOutlineError: brandColors.brandDarkRed,
};

// Combined color palette for easy access
export const palette = {
  ...brandColors,
  ...brandGradients,
  ...brandSupport,
  ...neutralColors,
  ...opacityColors,
  ...secondaryColors,
  ...backgroundColors,
  ...backgroundColoredColors,
  ...textColors,
  ...iconColors,
  ...borderColors,
  ...linkColors,
  ...feedbackColors,
  ...formColors,
};
</file>

<file path="index.ts">
/**
 * Export all design tokens
 */

import * as animation from './animation';
import * as borders from './borders';
import * as colors from './colors';
import * as shadows from './shadows';
import * as spacing from './spacing';
import * as typography from './typography';

export {
  animation,
  borders,
  colors,
  shadows,
  spacing,
  typography,
};

// Combined tokens object
const tokens = {
  animation,
  borders,
  colors,
  shadows,
  spacing,
  typography,
};

export default tokens;
</file>

<file path="shadows.ts">
/**
 * Shadow tokens
 */

import { borderWidths } from "./borders";
import { borderColors, formColors } from "./colors";

// Border shadows (outlines)
export const borderShadows = {
  shadowHover: {
    boxShadow: {
      color: borderColors.outlineHover,
      offset: { width: 0, height: 0 },
      opacity: 1,
      radius: borderWidths.l,
    },
    elevation: 0,
  },
  shadowError: {
    boxShadow: {
      color: formColors.formOutlineError,
      offset: { width: 0, height: 0 },
      opacity: 1,
      radius: borderWidths.l,
    },
    elevation: 0,
  },
  shadowSelected: {
    boxShadow: {
      color: borderColors.borderSelected,
      offset: { width: 0, height: 0 },
      opacity: 1,
      radius: borderWidths.m,
    },
    elevation: 0,
  },
};

// Box shadows
export const boxShadows = {
  xs: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.2)",
      offset: { width: 0, height: 3 },
      opacity: 1,
      radius: 5,
    },
    elevation: 3,
  },
  s: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.1)",
      offset: { width: 0, height: 5 },
      opacity: 1,
      radius: 10,
    },
    elevation: 6,
  },
  m: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.05)",
      offset: { width: 0, height: 10 },
      opacity: 1,
      radius: 15,
    },
    elevation: 10,
  },
  l: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.2)",
      offset: { width: 0, height: 10 },
      opacity: 1,
      radius: 30,
    },
    elevation: 15,
  },
};

// Component-specific shadows
export const componentShadows = {
  card: boxShadows.s,
  modal: boxShadows.l,
  dropdown: boxShadows.m,
  button: boxShadows.xs,
};
</file>

<file path="spacing.ts">
/**
 * Spacing tokens
 */

// Base grid unit
export const GRID = 4;

// Spacing scale
export const space = {
  0: 0,
  1: GRID * 1,
  2: GRID * 2,
  3: GRID * 3,
  4: GRID * 4,
  5: GRID * 5,
  6: GRID * 6,
  7: GRID * 7,
  8: GRID * 8,
  10: GRID * 10,
  12: GRID * 12,
  16: GRID * 16,
  24: GRID * 24,
  32: GRID * 32,
};

// Icon sizes
export const iconSizes = {
  small: 16,
  medium: 24,
  large: 32,
  extraLarge: 64,
  illustrationSmall: 40,
  illustrationMedium: 60,
  illustrationLarge: 80,
};

// Sizes for components and layouts
export const sizes = {
  1: 768,
  2: 1024,
  3: 1220,
  4: 1432,
  5: 1680,
  buttonMinWidth: 80,
  targetMinWidth: 44,
  targetMinHeight: 44,
  inputMinHeight: 48,
  ...iconSizes,
};

// Helper function to get spacing value
export const getSpacing = (multiplier: number): number => {
  return GRID * multiplier;
};

// Helper function to get responsive spacing
export const getResponsiveSpacing = (
  base: number,
  compact: number = base / 2
): { compact: number; base: number } => {
  return {
    compact: getSpacing(compact),
    base: getSpacing(base),
  };
};
</file>

<file path="typography.ts">
/**
 * Typography tokens
 */

// Base font size
export const BASE_FONT_SIZE = 10;
export const BASE_FONT_RELATIVE = `${(BASE_FONT_SIZE / 16) * 100}%`;

// Font families
export const fontFamilies = {
  base: "Etelka",
  heading: "Etelka",
};

// Font weights
export const fontWeights = {
  light: "300",
  medium: "500",
  bold: "700",
  black: "900",

  // Semantic weights
  bodyRegular: "300",
  bodyBold: "500",
  headingLight: "500",
  headingRegular: "700",
};

// Font sizes
export const fontSizes = {
  // Numeric sizes
  12: 12,
  14: 14,
  16: 16,
  18: 18,
  20: 20,
  24: 24,
  32: 32,
  40: 40,
  48: 48,
  64: 64,
  80: 80,

  // Semantic sizes
  "3XS": 16,
  "2XS": 18,
  XS: 20,
  S: 24,
  M: 32,
  L: 40,
  XL: 48,
  "2XL": 64,
  "3XL": 80,

  // Body sizes
  BodyXS: 12,
  BodyS: 14,
  BodyM: 16,
  BodyL: 18,
  BodyXL: 20,
};

// Letter spacings
export const letterSpacings = {
  narrowM: -2,
  narrowS: -1,
  normal: 0,
  wide: 0.3,
};

// Line heights
export const lineHeights = {
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  9: 36,
  10: 40,
  12: 48,
  16: 64,
  20: 80,
};

// Typography variants for body text
export const bodyTypography = {
  bodyXL: {
    fontSize: fontSizes.BodyXL,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyL: {
    fontSize: fontSizes.BodyL,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyM: {
    fontSize: fontSizes.BodyM,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[6],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyS: {
    fontSize: fontSizes.BodyS,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[5],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyXS: {
    fontSize: fontSizes.BodyXS,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[4],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  quoteM: {
    fontSize: fontSizes[32],
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[9],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  quoteS: {
    fontSize: fontSizes[24],
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
};

// Typography variants for headings
export const headingTypography = {
  heading3xl: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["3XL"],
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowM,
    lineHeight: lineHeights[20],
  },
  heading2xl: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["2XL"],
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowM,
    lineHeight: lineHeights[16],
  },
  headingXl: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.XL,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowM,
    lineHeight: lineHeights[12],
  },
  headingL: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.L,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowS,
    lineHeight: lineHeights[10],
  },
  headingM: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.M,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[9],
  },
  headingS: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.S,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
  },
  headingXs: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.XS,
    fontWeight: fontWeights.headingLight,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[6],
  },
  heading2xs: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["2XS"],
    fontWeight: fontWeights.headingLight,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[6],
  },
  heading3xs: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["3XS"],
    fontWeight: fontWeights.headingLight,
    letterSpacing: letterSpacings.wide,
    lineHeight: lineHeights[5],
  },
};

// Map to React Native Paper typography variants
export const paperTypographyMapping = {
  displayLarge: headingTypography.heading3xl,
  displayMedium: headingTypography.heading2xl,
  displaySmall: headingTypography.headingXl,

  headlineLarge: headingTypography.headingL,
  headlineMedium: headingTypography.headingM,
  headlineSmall: headingTypography.headingS,

  titleLarge: headingTypography.headingXs,
  titleMedium: headingTypography.heading2xs,
  titleSmall: headingTypography.heading3xs,

  bodyLarge: bodyTypography.bodyXL,
  bodyMedium: bodyTypography.bodyM,
  bodySmall: bodyTypography.bodyS,

  labelLarge: bodyTypography.bodyL,
  labelMedium: bodyTypography.bodyM,
  labelSmall: bodyTypography.bodyXS,
};
</file>

</files>
