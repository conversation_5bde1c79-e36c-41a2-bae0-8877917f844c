This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by <PERSON>omix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/**/*.ts, **/*.tsx, *.json
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
components/
  Badge.tsx
  Bleed.tsx
  Box.tsx
  Button.tsx
  ButtonInner.tsx
  ButtonLink.tsx
  Card.tsx
  Grid.tsx
  Heading.tsx
  Hidden.tsx
  IconButton.tsx
  Image.tsx
  PageGrid.tsx
  Skeleton.tsx
  Stack.tsx
  Text.tsx
  TextLink.tsx
  VisuallyHidden.tsx
provider.tsx
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="components/Badge.tsx">
/**
 * Badge component for the design system
 * Theme-aware implementation
 */

import React from 'react';
import { TextStyle, View, ViewStyle } from 'react-native';
import { useAppTheme } from '../provider';
import { Text } from './Text';

// Badge color variants
export type BadgeColorVariant = 'alpha' | 'success' | 'warning' | 'error' | 'highemphasis';

// Badge size variants
export type BadgeSizeVariant = 'S' | 'M';

// Badge props
export interface BadgeProps {
  /** Color variant of the badge */
  color?: BadgeColorVariant;
  /** Size variant of the badge */
  size?: BadgeSizeVariant;
  /** Content of the badge */
  children: string;
  /** Custom style for the badge */
  style?: ViewStyle;
  /** Custom style for the text */
  textStyle?: TextStyle;
  /** Whether the badge should be visible */
  visible?: boolean;
}

/**
 * Badge component
 *
 * Visual indicator used to highlight an item.
 */
export const Badge: React.FC<BadgeProps> = ({
  color = 'alpha',
  size = 'S',
  children,
  style,
  textStyle,
  visible = true,
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();

  if (!visible) {
    return null;
  }

  // Helper functions for styles
  const getColorStyle = (colorVariant: BadgeColorVariant): ViewStyle => {
    switch (colorVariant) {
      case 'alpha':
        return { backgroundColor: theme.colors.feedbackBackgroundSuccess };
      case 'success':
        return { backgroundColor: theme.colors.green100 };
      case 'warning':
        return { backgroundColor: theme.colors.yellow100 };
      case 'error':
        return { backgroundColor: theme.colors.pink100 };
      case 'highemphasis':
        return { backgroundColor: theme.colors.neutral900 };
      default:
        return { backgroundColor: theme.colors.feedbackBackgroundSuccess };
    }
  };

  const getTextColorStyle = (colorVariant: BadgeColorVariant): TextStyle => {
    switch (colorVariant) {
      case 'alpha':
        return { color: theme.colors.textOnBackgroundVarFive };
      case 'success':
        return { color: theme.colors.green900 };
      case 'warning':
        return { color: theme.colors.yellow900 };
      case 'error':
        return { color: theme.colors.pink900 };
      case 'highemphasis':
        return { color: theme.colors.neutralWhite };
      default:
        return { color: theme.colors.textOnBackgroundVarFive };
    }
  };

  const getSizeStyle = (sizeVariant: BadgeSizeVariant): ViewStyle => {
    switch (sizeVariant) {
      case 'S':
        return {}; // Small size (default)
      case 'M':
        return {}; // Medium size
      default:
        return {}; // Small size
    }
  };

  const getTextSizeStyle = (sizeVariant: BadgeSizeVariant): TextStyle => {
    switch (sizeVariant) {
      case 'S':
        return {
          fontSize: theme.typography.labelSmall.fontSize,
          lineHeight: theme.typography.labelSmall.lineHeight,
        };
      case 'M':
        return {
          fontSize: theme.typography.bodySmall.fontSize,
          lineHeight: theme.typography.bodySmall.lineHeight,
        };
      default:
        return {
          fontSize: theme.typography.labelSmall.fontSize,
          lineHeight: theme.typography.labelSmall.lineHeight,
        };
    }
  };

  // Dynamic styles that depend on theme
  const styles = {
    badge: {
      borderRadius: theme.borders.radii.round,
      paddingHorizontal: theme.spacing[3],
      paddingVertical: theme.spacing[1],
      alignSelf: 'flex-start' as const,
    },
    text: {
      fontFamily: theme.typography.bodyMedium.fontFamily,
      fontWeight: 'bold' as const,
      textAlign: 'center' as const,
    },
  };

  return (
    <View
      style={[
        styles.badge,
        getColorStyle(color),
        getSizeStyle(size),
        style,
      ]}
      accessibilityRole="text"
    >
      <Text
        style={[
          styles.text,
          getTextColorStyle(color),
          getTextSizeStyle(size),
          textStyle,
        ]}
      >
        {children}
      </Text>
    </View>
  );
};

export default Badge;
</file>

<file path="components/Bleed.tsx">
/**
 * Bleed component for the design system
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import tokens from '../tokens';

// Spacing values
type SpacingValue = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '8' | '10' | '12' | '16' | '24' | '32';

// Special horizontal value for grid gutters
type HorizontalValue = SpacingValue | 'gridGutter';

// Bleed props
export interface BleedProps extends ViewProps {
  /** Adds negative margins to the left and right of the child */
  horizontal?: HorizontalValue;
  /** Adds negative margins to the top and bottom of the child */
  vertical?: SpacingValue;
  /** Adds negative margins to the top of the child */
  top?: SpacingValue;
  /** Adds negative margins to the bottom of the child */
  bottom?: SpacingValue;
  /** Adds negative margins to the left of the child */
  left?: SpacingValue;
  /** Adds negative margins to the right of the child */
  right?: SpacingValue;
  /** Child components */
  children: React.ReactNode;
}

// Bleed component
export const Bleed: React.FC<BleedProps> = ({
  horizontal,
  vertical,
  top,
  bottom,
  left,
  right,
  style,
  children,
  ...rest
}) => {
  // Get negative margin value based on spacing token
  const getNegativeMargin = (value: SpacingValue | undefined): number | undefined => {
    if (!value) return undefined;
    return -tokens.spacing.space[value];
  };

  // Handle special gridGutter case
  const getHorizontalMargin = (): ViewStyle | undefined => {
    if (horizontal === 'gridGutter') {
      // In React Native, we'll use a simpler approach for the gridGutter
      // since we don't have the same CSS media queries
      return {
        marginHorizontal: -16, // Default gutter size
      };
    }
    
    if (horizontal) {
      return {
        marginHorizontal: getNegativeMargin(horizontal as SpacingValue),
      };
    }
    
    return undefined;
  };

  // Create style object with all margins
  const bleedStyle: ViewStyle = {
    ...(vertical && { marginVertical: getNegativeMargin(vertical) }),
    ...(top && { marginTop: getNegativeMargin(top) }),
    ...(bottom && { marginBottom: getNegativeMargin(bottom) }),
    ...(left && { marginLeft: getNegativeMargin(left) }),
    ...(right && { marginRight: getNegativeMargin(right) }),
    ...getHorizontalMargin(),
  };

  return (
    <View style={[bleedStyle, style]} {...rest}>
      {children}
    </View>
  );
};

export default Bleed;
</file>

<file path="components/Box.tsx">
/**
 * Box component for the design system
 * Theme-aware implementation
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { useAppTheme } from '../provider';

// Spacing values
type SpacingValue = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '10' | '12' | '16' | '24' | '32';

// Border radius values
type BorderRadiusValue = 's' | 'm' | 'l' | 'none' | 'round';

// Background color values
type BackgroundColorValue =
  | 'backgroundPrimary'
  | 'backgroundSecondary'
  | 'backgroundTertiary'
  | 'backgroundScrim'
  | 'backgroundDark'
  | 'backgroundPressed'
  | 'feedbackBackgroundError'
  | 'feedbackBackgroundSuccess'
  | 'feedbackBackgroundWarning'
  | 'feedbackBackgroundInfo';

// Overflow values
type OverflowValue = 'hidden' | 'visible' | 'scroll';

// Box props
export interface BoxProps extends ViewProps {
  /** Background color of the box */
  backgroundColor?: BackgroundColorValue;
  /** Border radius of all corners */
  borderRadius?: BorderRadiusValue;
  /** Border radius of top left corner */
  borderTopLeftRadius?: BorderRadiusValue;
  /** Border radius of top right corner */
  borderTopRightRadius?: BorderRadiusValue;
  /** Border radius of bottom left corner */
  borderBottomLeftRadius?: BorderRadiusValue;
  /** Border radius of bottom right corner */
  borderBottomRightRadius?: BorderRadiusValue;
  /** Overflow behavior */
  overflow?: OverflowValue;
  /** Padding on all sides */
  padding?: SpacingValue;
  /** Padding on top and bottom */
  paddingY?: SpacingValue;
  /** Padding on left and right */
  paddingX?: SpacingValue;
  /** Padding on top */
  paddingTop?: SpacingValue;
  /** Padding on bottom */
  paddingBottom?: SpacingValue;
  /** Padding on left */
  paddingLeft?: SpacingValue;
  /** Padding on right */
  paddingRight?: SpacingValue;
  /** Child components */
  children: React.ReactNode;
}

// Box component
export const Box = React.forwardRef<View, BoxProps>(
  (
    {
      backgroundColor,
      borderRadius,
      borderTopLeftRadius,
      borderTopRightRadius,
      borderBottomLeftRadius,
      borderBottomRightRadius,
      overflow,
      padding,
      paddingY,
      paddingX,
      paddingTop,
      paddingBottom,
      paddingLeft,
      paddingRight,
      style,
      children,
      ...rest
    },
    ref
  ) => {
    // Use theme instead of direct token references
    const { theme } = useAppTheme();

    // Get background color
    const getBackgroundColor = (): string | undefined => {
      if (!backgroundColor) return undefined;

      // Map background color to theme value
      switch (backgroundColor) {
        case 'backgroundPrimary':
          return theme.colors.backgroundPrimary;
        case 'backgroundSecondary':
          return theme.colors.backgroundSecondary;
        case 'backgroundTertiary':
          return theme.colors.backgroundTertiary;
        case 'backgroundScrim':
          return theme.colors.backgroundScrim;
        case 'backgroundDark':
          return theme.colors.backgroundDark;
        case 'backgroundPressed':
          return theme.colors.backgroundPressed;
        case 'feedbackBackgroundError':
          return theme.colors.feedbackBackgroundError;
        case 'feedbackBackgroundSuccess':
          return theme.colors.feedbackBackgroundSuccess;
        case 'feedbackBackgroundWarning':
          return theme.colors.feedbackBackgroundWarning;
        case 'feedbackBackgroundInfo':
          return theme.colors.feedbackBackgroundInfo;
        default:
          return undefined;
      }
    };

    // Get border radius value
    const getBorderRadiusValue = (value?: BorderRadiusValue): number | undefined => {
      if (!value) return undefined;
      if (value === 'none') return 0;

      return theme.borders.radii[value];
    };

    // Get padding value
    const getPaddingValue = (value?: SpacingValue): number | undefined => {
      if (!value) return undefined;
      return theme.spacing[value];
    };

    // Create style object
    const boxStyle: ViewStyle = {
      ...(backgroundColor && { backgroundColor: getBackgroundColor() }),
      ...(borderRadius && { borderRadius: getBorderRadiusValue(borderRadius) }),
      ...(borderTopLeftRadius && { borderTopLeftRadius: getBorderRadiusValue(borderTopLeftRadius) }),
      ...(borderTopRightRadius && { borderTopRightRadius: getBorderRadiusValue(borderTopRightRadius) }),
      ...(borderBottomLeftRadius && { borderBottomLeftRadius: getBorderRadiusValue(borderBottomLeftRadius) }),
      ...(borderBottomRightRadius && { borderBottomRightRadius: getBorderRadiusValue(borderBottomRightRadius) }),
      ...(overflow && { overflow }),
      ...(padding && { padding: getPaddingValue(padding) }),
      ...(paddingY && { paddingVertical: getPaddingValue(paddingY) }),
      ...(paddingX && { paddingHorizontal: getPaddingValue(paddingX) }),
      ...(paddingTop && { paddingTop: getPaddingValue(paddingTop) }),
      ...(paddingBottom && { paddingBottom: getPaddingValue(paddingBottom) }),
      ...(paddingLeft && { paddingLeft: getPaddingValue(paddingLeft) }),
      ...(paddingRight && { paddingRight: getPaddingValue(paddingRight) }),
    };

    return (
      <View ref={ref} style={[boxStyle, style]} {...rest}>
        {children}
      </View>
    );
  }
);

Box.displayName = 'Box';

export default Box;
</file>

<file path="components/Button.tsx">
/**
 * Button component for the design system
 * Theme-aware implementation
 */

import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  GestureResponderEvent,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from "react-native";
import { StyleSheet } from "react-native-unistyles";
import { useAppTheme } from "../provider";
import tokens from "../tokens";
import { Text } from "./Text";

// Button action variants
export type ButtonAction = "primary" | "secondary" | "primaryRebrand";

// Button sizes
export type ButtonSize = "compact" | "regular";

// Button tone
export type ButtonTone = "onLight" | "onColor";

// Button props
export interface ButtonProps extends Omit<TouchableOpacityProps, "children"> {
  /** Emphasises the importance of your action with colors */
  action?: ButtonAction;
  /** The size of the Button */
  size?: ButtonSize;
  /** Changes the tone based on the background color. Note: This **only** has an effect on **primary** action buttons. */
  tone?: ButtonTone;
  /** Signals the user has to wait before they can press the button. While this is true, the button is disabled. */
  isLoading?: boolean;
  /** Hidden helper text for screenreaders when the button is in the loading state */
  loadingText?: string;
  /** Hidden helper text for screenreaders when the button is no longer in the loading state */
  finishedLoadingText?: string;
  /** Button content */
  children: React.ReactNode;
  /** Optional icon to display before the text */
  icon?: React.ReactNode;
  /** Optional style for the button content */
  contentStyle?: StyleProp<ViewStyle>;
  /** Optional style for the button text */
  labelStyle?: StyleProp<TextStyle>;
  /** Optional button color override */
  buttonColor?: string;
  /** Optional text color override */
  textColor?: string;
  /** Optional onPress handler */
  onPress?: (e: GestureResponderEvent) => void;
}

// Button component
export const Button: React.FC<ButtonProps> = ({
  action = "primary",
  size = "regular",
  tone = "onLight",
  isLoading = false,
  loadingText,
  finishedLoadingText,
  disabled = false,
  style,
  contentStyle,
  labelStyle,
  buttonColor,
  textColor,
  icon,
  children,
  onPress,
  ...rest
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();
  const [helperText, setHelperText] = useState("");

  // Handle loading state and accessibility text
  useEffect(() => {
    if (!isLoading && helperText !== "") {
      setHelperText(finishedLoadingText ?? "Finished loading");
    }
    if (isLoading) {
      setHelperText(loadingText ?? "Loading");
    }
  }, [isLoading, helperText, loadingText, finishedLoadingText]);

  // Get border radius based on action
  const getBorderRadius = () => {
    if (action === "primaryRebrand") {
      return theme.borders.components.button.borderRadiusRebrand;
    }
    return theme.borders.components.button.borderRadiusDefault;
  };

  // Get button background color based on action
  const getButtonBackgroundColor = () => {
    if (action === "primary" && tone === "onColor") {
      return theme.colors.backgroundPrimary;
    }
    if (action === "primary" || action === "primaryRebrand") {
      return theme.colors.primary;
    }
    return "transparent"; // For secondary buttons
  };

  // Get button border color and width
  const getButtonBorder = () => {
    if (action === "secondary") {
      return {
        borderWidth: theme.borders.widths.m,
        borderColor: theme.colors.primary,
      };
    }
    return {};
  };

  // Get text color based on action
  const getTextColor = () => {
    if (action === "primary" && tone === "onColor") {
      return theme.colors.textBrand;
    }
    if (action === "secondary") {
      return theme.colors.textBrand;
    }
    if (action === "primary" || action === "primaryRebrand") {
      return theme.colors.onPrimary;
    }
    return theme.colors.textPrimary;
  };

  // Get size styles
  const getSizeStyles = () => {
    return size === "compact" ? styles.compactContent : styles.regularContent;
  };

  // Determine if we should show a chevron
  const shouldShowChevron = () => {
    return action === "primary" && size === "regular" && !isLoading;
  };

  // Create a custom loading indicator that matches the original design
  const renderLoadingIndicator = () => {
    if (!isLoading) return null;

    const color =
      action === "primary"
        ? theme.colors.onPrimary
        : theme.colors.textBrand;

    return (
      <ActivityIndicator
        size="small"
        color={color}
        style={styles.loadingIndicator}
      />
    );
  };

  // Render the button inner content
  const renderInnerContent = () => {
    return (
      <View style={[styles.buttonInner, getSizeStyles(), contentStyle]}>
        {icon && <View>{icon}</View>}
        <Text
          style={[
            styles.buttonText,
            { color: textColor || getTextColor() },
            labelStyle,
          ]}
        >
          {children}
        </Text>
        {shouldShowChevron() && (
          <Text style={[styles.chevron, { color: getTextColor() }]}>
            {'\u203A'} {/* Unicode for › character */}
          </Text>
        )}
      </View>
    );
  };

  // Get loading background color
  const getLoadingBackgroundColor = () => {
    return isLoading ? theme.colors.secondary : undefined;
  };

  // Dynamic styles that depend on theme
  const buttonStyles = {
    button: {
      alignItems: "center" as const,
      justifyContent: "center" as const,
      minWidth: theme.spacing[10],
      position: "relative" as const,
      overflow: "hidden" as const,
    },
    buttonInner: {
      alignItems: "center" as const,
      flexDirection: "row" as const,
      justifyContent: "center" as const,
      gap: theme.spacing[3],
    },
    buttonText: {
      fontFamily: tokens.typography.fontFamilies.base,
      fontWeight: "700" as const,
    },
    compactContent: {
      paddingVertical: theme.spacing[3],
      paddingHorizontal: theme.spacing[4],
    },
    regularContent: {
      paddingVertical: theme.spacing[4],
      paddingHorizontal: theme.spacing[4],
    },
    chevron: {
      fontSize: 20,
      fontWeight: "700" as const,
      marginLeft: theme.spacing[2],
    },
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      disabled={disabled || isLoading}
      onPress={onPress}
      style={[
        buttonStyles.button,
        {
          backgroundColor: buttonColor || getButtonBackgroundColor(),
          borderRadius: getBorderRadius(),
          ...getButtonBorder(),
        },
        isLoading && { backgroundColor: getLoadingBackgroundColor() },
        (disabled || isLoading) && styles.disabledButton,
        style,
      ]}
      accessibilityState={{
        disabled: disabled || isLoading,
        busy: isLoading,
      }}
      accessibilityLabel={isLoading ? helperText : undefined}
      {...rest}
    >
      {renderInnerContent()}
      {renderLoadingIndicator()}
    </TouchableOpacity>
  );
};

// Static styles that don't depend on theme
const styles = StyleSheet.create({
  disabledButton: {
    opacity: 0.5,
  },
  loadingIndicator: {
    position: "absolute",
  },
  compactButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  regularButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  compactContent: {
    paddingVertical: tokens.spacing.space[3],
    paddingHorizontal: tokens.spacing.space[4],
  },
  regularContent: {
    paddingVertical: tokens.spacing.space[4],
    paddingHorizontal: tokens.spacing.space[4],
  },
  buttonInner: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: tokens.spacing.space[3],
  },
  buttonText: {
    fontFamily: tokens.typography.fontFamilies.base,
    fontWeight: "700",
  },
  chevron: {
    fontSize: 20,
    fontWeight: "700",
    marginLeft: tokens.spacing.space[2],
  },
});

export default Button;
</file>

<file path="components/ButtonInner.tsx">
/**
 * ButtonInner component for the design system
 */

import React, { FC, ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';
import tokens from '../tokens';
import { Text } from './Text';

// Check if children contains an icon
const hasIcon = (children: ReactNode): boolean => {
  if (React.Children.count(children) <= 1) return false;

  // In React Native, we'll consider the first child as an icon if there are multiple children
  return React.Children.count(children) > 1;
};

interface ButtonInnerProps {
  children: ReactNode;
  'aria-hidden'?: boolean;
}

export const ButtonInner: FC<ButtonInnerProps> = ({ children, 'aria-hidden': ariaHidden }) => {
  return (
    <View
      style={styles.buttonInner}
      accessibilityElementsHidden={ariaHidden}
      importantForAccessibility={ariaHidden ? 'no-hide-descendants' : 'auto'}
    >
      {children}
      {!hasIcon(children) && (
        <View style={styles.chevronIconWrapper}>
          <Text style={styles.chevron}>
            {'\u203A'} {/* Unicode for › character */}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonInner: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: tokens.spacing.space[3],
    // fontWeight should be in Text style, not View style
  },
  chevronIconWrapper: {
    // This will be conditionally displayed based on button variant
  },
  chevron: {
    fontSize: 20,
    fontWeight: 'bold',
  },
});

export default ButtonInner;
</file>

<file path="components/ButtonLink.tsx">
/**
 * ButtonLink component for the design system
 */

import React from 'react';
import {
  GestureResponderEvent,
  Linking,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import tokens from '../tokens';
import { Text } from './Text';
import VisuallyHidden from './VisuallyHidden';

// ButtonLink action variants
export type ButtonLinkAction = 'primary' | 'secondary' | 'primaryRebrand';

// ButtonLink sizes
export type ButtonLinkSize = 'compact' | 'regular';

// ButtonLink tone
export type ButtonLinkTone = 'onLight' | 'onColor';

// ButtonLink props
export interface ButtonLinkProps extends Omit<TouchableOpacityProps, 'children'> {
  /** Emphasises the importance of your action with colors */
  action?: ButtonLinkAction;
  /** The size of the ButtonLink */
  size?: ButtonLinkSize;
  /** Changes the tone based on the background color. Note: This **only** has an effect on **primary** action buttons. */
  tone?: ButtonLinkTone;
  /** URL to navigate to when pressed */
  href: string;
  /** Target for the link (e.g., '_blank') */
  target?: string;
  /** Accessible label for the button */
  label?: string;
  /** Button content */
  children: React.ReactNode;
  /** Optional style for the button content */
  contentStyle?: StyleProp<ViewStyle>;
  /** Optional style for the button text */
  labelStyle?: StyleProp<TextStyle>;
  /** Optional button color override */
  buttonColor?: string;
  /** Optional text color override */
  textColor?: string;
  /** Optional onPress handler */
  onPress?: (e: GestureResponderEvent) => void;
}

// ButtonLink styles
const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: tokens.spacing.sizes.buttonMinWidth,
    position: 'relative',
    overflow: 'hidden',
  },
  buttonInner: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: tokens.spacing.space[3],
  },
  buttonText: {
    fontFamily: tokens.typography.fontFamilies.base,
    fontWeight: '700',
  },
  compactButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  regularButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  compactContent: {
    paddingVertical: tokens.spacing.space[3],
    paddingHorizontal: tokens.spacing.space[4],
  },
  regularContent: {
    paddingVertical: tokens.spacing.space[4],
    paddingHorizontal: tokens.spacing.space[4],
  },
  chevron: {
    fontSize: 20,
    fontWeight: '700',
    marginLeft: tokens.spacing.space[2],
  },
});

// ButtonLink component
export const ButtonLink: React.FC<ButtonLinkProps> = ({
  action = 'primary',
  size = 'regular',
  tone = 'onLight',
  href,
  target,
  label,
  style,
  contentStyle,
  labelStyle,
  buttonColor,
  textColor,
  children,
  onPress,
  ...rest
}) => {
  // Handle press event
  const handlePress = (event: GestureResponderEvent) => {
    if (onPress) {
      onPress(event);
    }
    
    if (href) {
      Linking.openURL(href);
    }
  };

  // Get border radius based on action
  const getBorderRadius = () => {
    if (action === 'primaryRebrand') {
      return tokens.borders.componentBorders.button.borderRadiusRebrand;
    }
    return tokens.borders.componentBorders.button.borderRadiusDefault;
  };

  // Get button background color based on action
  const getButtonBackgroundColor = () => {
    if (action === 'primary' && tone === 'onColor') {
      return tokens.colors.backgroundColors.backgroundPrimary;
    }
    if (action === 'primary' || action === 'primaryRebrand') {
      return tokens.colors.secondaryColors.accentGreen700;
    }
    return 'transparent'; // For secondary buttons
  };

  // Get button border color and width
  const getButtonBorder = () => {
    if (action === 'secondary') {
      return {
        borderWidth: tokens.borders.borderWidths.m,
        borderColor: tokens.colors.secondaryColors.accentGreen700,
      };
    }
    return {};
  };

  // Get text color based on action
  const getTextColor = () => {
    if (action === 'primary' && tone === 'onColor') {
      return tokens.colors.textColors.textBrand;
    }
    if (action === 'secondary') {
      return tokens.colors.textColors.textBrand;
    }
    if (action === 'primary' || action === 'primaryRebrand') {
      return tokens.colors.neutralColors.neutralWhite;
    }
    return tokens.colors.textColors.textPrimary;
  };

  // Get size styles
  const getSizeStyles = () => {
    return size === 'compact' ? styles.compactContent : styles.regularContent;
  };

  // Determine if we should show a chevron
  const shouldShowChevron = () => {
    // In the original design, chevrons were shown for primary buttons
    // but not for secondary buttons or compact size
    return action === 'primary' && size === 'regular';
  };

  // Render the button content
  const renderContent = () => {
    // Check if children is a string or contains an icon
    let icon = null;
    let text = children;

    // If children is an array, check for icon
    if (React.Children.count(children) > 1) {
      const childrenArray = React.Children.toArray(children);
      // Assume first child is an icon if it's not a string
      if (typeof childrenArray[0] !== 'string') {
        icon = childrenArray[0];
        text = childrenArray.slice(1);
      }
    }

    return (
      <View style={[styles.buttonInner, getSizeStyles(), contentStyle]}>
        {icon && <View>{icon}</View>}
        {typeof text === 'string' ? (
          <Text
            style={[
              styles.buttonText,
              { color: textColor || getTextColor() },
              labelStyle,
            ]}
          >
            {text}
          </Text>
        ) : (
          text
        )}
        {shouldShowChevron() && (
          <Text style={[styles.chevron, { color: getTextColor() }]}>›</Text>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={handlePress}
      style={[
        styles.button,
        {
          backgroundColor: buttonColor || getButtonBackgroundColor(),
          borderRadius: getBorderRadius(),
          ...getButtonBorder(),
        },
        style,
      ]}
      accessibilityRole="link"
      accessibilityLabel={label || (typeof children === 'string' ? children : undefined)}
      accessibilityHint={target === '_blank' ? 'Opens in a new window' : undefined}
      {...rest}
    >
      {label && <VisuallyHidden>{label}</VisuallyHidden>}
      {renderContent()}
    </TouchableOpacity>
  );
};

export default ButtonLink;
</file>

<file path="components/Card.tsx">
/**
 * Card component for the design system
 * Theme-aware implementation
 */

import React, { ReactNode } from 'react';
import { Image, ImageSourcePropType, ImageStyle, StyleProp, View, ViewStyle } from 'react-native';
import { useAppTheme } from '../provider';
import tokens from '../tokens';
import { Box } from './Box';
import { Heading, Text } from './Text';

// Card corner variants
export type CardCorners = 'square' | 'rounded';

// Card elevation variants
export type CardElevation = 'S' | 'L';

// Card overflow variants
export type CardOverflow = 'hidden';

// Card props
export interface CardProps {
  /** Corner style of the card */
  corners?: CardCorners;
  /** Shadow elevation of the card */
  elevation?: CardElevation;
  /** Overflow behavior */
  overflow?: CardOverflow;
  /** Whether the card should take full width */
  fullWidth?: boolean;
  /** Custom style for the card */
  style?: StyleProp<ViewStyle>;
  /** Child components */
  children?: ReactNode;
}

// Card Title props
export interface CardTitleProps {
  title: string;
  subtitle?: string;
  left?: ReactNode;
  right?: ReactNode;
  style?: StyleProp<ViewStyle>;
}

// Card Content props
export interface CardContentProps {
  children?: ReactNode;
  style?: StyleProp<ViewStyle>;
}

// Card Cover props
export interface CardCoverProps {
  source: ImageSourcePropType;
  style?: StyleProp<ImageStyle>;
}

// Card Actions props
export interface CardActionsProps {
  children?: ReactNode;
  style?: StyleProp<ViewStyle>;
}

// Card component
export const Card: React.FC<CardProps> = ({
  corners = 'rounded',
  elevation = 'S',
  overflow,
  fullWidth = true,
  style,
  children,
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();

  // Get shadow style based on elevation
  const getShadowStyle = (): ViewStyle => {
    return elevation === 'L' ? tokens.shadows.boxShadows.l.boxShadow : tokens.shadows.boxShadows.s.boxShadow;
  };

  // Get corner style
  const getCornerStyle = (): ViewStyle => {
    return corners === 'rounded'
      ? { borderRadius: theme.borders.components.card.borderRadius }
      : { borderRadius: 0 };
  };

  // Get overflow style
  const getOverflowStyle = (): ViewStyle | undefined => {
    return overflow === 'hidden' ? { overflow: 'hidden' } : undefined;
  };

  // Dynamic styles that depend on theme
  const cardStyles = {
    card: {
      backgroundColor: theme.colors.background,
      width: '100%' as const,
      display: 'flex' as const,
      flexDirection: 'column' as const,
    },
    fullWidth: {
      width: '100%' as const,
    },
  };

  return (
    <View
      style={[
        cardStyles.card,
        getCornerStyle(),
        getShadowStyle(),
        getOverflowStyle(),
        fullWidth && cardStyles.fullWidth,
        style,
      ]}
    >
      {children}
    </View>
  );
};

// Card.Title component
export const CardTitle: React.FC<CardTitleProps> = ({
  title,
  subtitle,
  left,
  right,
  style,
}) => {
  const { theme } = useAppTheme();

  const titleStyles = {
    title: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      paddingHorizontal: theme.spacing[4],
      paddingVertical: theme.spacing[3],
    },
    titleContent: {
      flex: 1,
    },
    titleLeft: {
      marginRight: theme.spacing[3],
    },
    titleRight: {
      marginLeft: theme.spacing[3],
    },
  };

  return (
    <View style={[titleStyles.title, style]}>
      {left && <View style={titleStyles.titleLeft}>{left}</View>}
      <View style={titleStyles.titleContent}>
        <Heading level={6}>{title}</Heading>
        {subtitle && <Text variant="bodySmall">{subtitle}</Text>}
      </View>
      {right && <View style={titleStyles.titleRight}>{right}</View>}
    </View>
  );
};

// Card.Content component
export const CardContent: React.FC<CardContentProps> = ({
  children,
  style,
}) => {
  return <Box padding="8" style={style}>{children}</Box>;
};

// Card.Cover component
export const CardCover: React.FC<CardCoverProps> = ({
  source,
  style,
}) => {
  // Dynamic styles that depend on theme
  const coverStyles = {
    cover: {
      width: '100%' as const,
      height: 200,
    },
  };

  return (
    <Image
      source={source}
      style={[coverStyles.cover, style]}
      resizeMode="cover"
    />
  );
};

// Card.Actions component
export const CardActions: React.FC<CardActionsProps> = ({
  children,
  style,
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();

  // Dynamic styles that depend on theme
  const actionStyles = {
    actions: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'flex-end' as const,
      paddingHorizontal: theme.spacing[2],
      paddingVertical: theme.spacing[1],
    },
  };

  return <View style={[actionStyles.actions, style]}>{children}</View>;
};

// Add subcomponents to Card
const CardWithSubcomponents = Card as typeof Card & {
  Title: typeof CardTitle;
  Content: typeof CardContent;
  Cover: typeof CardCover;
  Actions: typeof CardActions;
};

CardWithSubcomponents.Title = CardTitle;
CardWithSubcomponents.Content = CardContent;
CardWithSubcomponents.Cover = CardCover;
CardWithSubcomponents.Actions = CardActions;

export default CardWithSubcomponents;
</file>

<file path="components/Grid.tsx">
/**
 * Grid component for the design system
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import tokens from '../tokens';

// Grid alignment values
type AlignValue = 'start' | 'center' | 'end' | 'stretch' | 'baseline';

// FlexAlign type for React Native
type FlexAlignType = 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';

// JustifyContent type for React Native
type JustifyContentType = 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';

// Grid flow values
type FlowValue = 'row' | 'column' | 'dense' | 'rowDense' | 'columnDense';

// Grid columns values
type ColumnsValue = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12';

// Grid gap values
type GapValue = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '8' | '10' | '12' | '16' | '24' | '32';

// Grid props
export interface GridProps extends ViewProps {
  /** Alignment of the grid items on the vertical axis */
  alignY?: AlignValue;
  /** Alignment of the grid items on the horizontal axis */
  alignX?: AlignValue;
  /** Justification of the grid content */
  justifyContent?: AlignValue;
  /** Flow direction of the grid items */
  flow?: FlowValue;
  /** Number of columns in the grid */
  columns?: ColumnsValue;
  /** Gap between grid items */
  gap?: GapValue;
  /** Gap between rows */
  rowGap?: GapValue;
  /** Gap between columns */
  columnGap?: GapValue;
  /** Whether the grid should be displayed inline */
  inline?: boolean;
  /** Grid template columns */
  gridTemplateColumns?: string;
  /** Grid template rows */
  gridTemplateRows?: string;
  /** Grid template areas */
  gridTemplateAreas?: string;
  /** Child components */
  children: React.ReactNode;
}

// Grid item props
export interface GridItemProps extends ViewProps {
  /** Grid area name */
  gridArea?: string;
  /** Grid column start */
  gridColumnStart?: string | number;
  /** Grid column end */
  gridColumnEnd?: string | number;
  /** Grid column span */
  gridColumn?: string;
  /** Grid row start */
  gridRowStart?: string | number;
  /** Grid row end */
  gridRowEnd?: string | number;
  /** Grid row span */
  gridRow?: string;
  /** Order of the grid item */
  order?: number;
  /** Alignment of the grid item */
  alignSelf?: AlignValue;
  /** Justification of the grid item */
  justifySelf?: AlignValue;
  /** Child components */
  children: React.ReactNode;
}

// Grid styles
const styles = StyleSheet.create({
  grid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  gridItem: {
    flexGrow: 0,
    flexShrink: 0,
  },
});

// GridItem component
const GridItem: React.FC<GridItemProps> = ({
  gridArea,
  gridColumnStart,
  gridColumnEnd,
  gridColumn,
  gridRowStart,
  gridRowEnd,
  gridRow,
  order,
  alignSelf,
  justifySelf,
  style,
  children,
  ...rest
}) => {
  // Parse grid column value
  const parseGridColumn = (): ViewStyle => {
    if (!gridColumn) return {};

    // Simple implementation for common patterns
    if (gridColumn === '1/-1' || gridColumn === '1 / -1') {
      return { width: '100%' };
    }

    // For more complex patterns, we'd need a more sophisticated parser
    return {};
  };

  // Get flex basis based on grid column span
  const getFlexBasis = (): number | undefined => {
    if (gridColumn) {
      const parts = gridColumn.split('/').map(p => p.trim());
      if (parts.length === 2) {
        const start = parseInt(parts[0], 10);
        const end = parseInt(parts[1], 10);
        if (!isNaN(start) && !isNaN(end)) {
          const span = end - start;
          // Return as a percentage (0-1) for React Native
          return (span / 12) * 100;
        }
      }
    }
    return undefined;
  };

  // Convert alignSelf to React Native compatible value
  const getAlignSelf = (): FlexAlignType | 'auto' | undefined => {
    if (!alignSelf) return undefined;

    switch (alignSelf) {
      case 'start': return 'flex-start';
      case 'center': return 'center';
      case 'end': return 'flex-end';
      case 'stretch': return 'stretch';
      case 'baseline': return 'baseline';
      default: return undefined;
    }
  };

  // Create style object
  const itemStyle: ViewStyle = {
    ...(order !== undefined && { order }),
    ...(alignSelf && { alignSelf: getAlignSelf() }),
    ...parseGridColumn(),
    ...(getFlexBasis() && { flexBasis: getFlexBasis() }),
  };

  return (
    <View style={[styles.gridItem, itemStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// Grid component
const GridComponent: React.FC<GridProps> = ({
  alignY,
  alignX,
  justifyContent,
  flow,
  columns,
  gap,
  rowGap,
  columnGap,
  inline,
  gridTemplateColumns,
  gridTemplateRows,
  gridTemplateAreas,
  style,
  children,
  ...rest
}) => {
  // Get alignment style
  const getAlignItems = (): FlexAlignType | undefined => {
    if (!alignY) return undefined;

    switch (alignY) {
      case 'start': return 'flex-start';
      case 'center': return 'center';
      case 'end': return 'flex-end';
      case 'stretch': return 'stretch';
      case 'baseline': return 'baseline';
      default: return undefined;
    }
  };

  // Get justify items style
  const getJustifyItems = (): JustifyContentType | undefined => {
    if (!alignX) return undefined;

    switch (alignX) {
      case 'start': return 'flex-start';
      case 'center': return 'center';
      case 'end': return 'flex-end';
      // 'stretch' is not valid for justifyContent in React Native
      case 'stretch': return 'center';
      default: return undefined;
    }
  };

  // Get gap value
  const getGapValue = (value?: GapValue): number | undefined => {
    if (!value) return undefined;
    return tokens.spacing.space[value];
  };

  // Create style object
  const gridStyle: ViewStyle = {
    ...(alignY && { alignItems: getAlignItems() }),
    ...(alignX && { justifyContent: getJustifyItems() }),
    ...(gap && { gap: getGapValue(gap) }),
    ...(rowGap && { rowGap: getGapValue(rowGap) }),
    ...(columnGap && { columnGap: getGapValue(columnGap) }),
  };

  return (
    <View style={[styles.grid, gridStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// Add GridItem to Grid component
type GridComponentType = typeof GridComponent & {
  Item: typeof GridItem;
};

// Create Grid with GridItem
const Grid = GridComponent as GridComponentType;
Grid.Item = GridItem;

// Display names
GridComponent.displayName = 'Grid';
GridItem.displayName = 'Grid.Item';

export default Grid;
</file>

<file path="components/Heading.tsx">
/**
 * Heading component for the design system
 */

import React from 'react';
import { Text as RNText, TextProps as RNTextProps, TextStyle } from 'react-native';
import tokens from '../tokens';

// Heading size variants
export type HeadingSize = '3XS' | '2XS' | 'XS' | 'S' | 'M' | 'L' | 'XL' | '2XL' | '3XL';

// Heading level variants
export type HeadingLevel = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

// Heading props
export interface HeadingProps extends Omit<RNTextProps, 'children'> {
  /** The visual size of the heading */
  size?: HeadingSize;
  /** The semantic level of the heading (for accessibility) */
  as: HeadingLevel;
  /** The color of the heading */
  color?: string;
  /** ID for the heading (for accessibility) */
  id?: string;
  /** Child components */
  children: React.ReactNode;
}

// Heading component
export const Heading: React.FC<HeadingProps> = ({
  size = 'XL',
  as = 'h1',
  color = tokens.colors.textColors.textPrimary,
  id,
  children,
  style,
  ...rest
}) => {
  // Map heading size to typography style
  const getTypographyStyle = (): TextStyle => {
    switch (size) {
      case '3XL':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['3XL'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[20],
        };
      case '2XL':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['2XL'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[16],
        };
      case 'XL':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.XL,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[12],
        };
      case 'L':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.L,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[10],
        };
      case 'M':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.M,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[9],
        };
      case 'S':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.S,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[7],
        };
      case 'XS':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.XS,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[6],
        };
      case '2XS':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['2XS'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[6],
        };
      case '3XS':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['3XS'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[5],
        };
      default:
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.XL,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[12],
        };
    }
  };

  // Map heading level to accessibility role
  const getAccessibilityRole = () => {
    return 'heading';
  };

  // Get heading level as number for accessibility
  const getHeadingLevel = (): number => {
    return parseInt(as.substring(1), 10);
  };

  return (
    <RNText
      style={[
        getTypographyStyle(),
        { color },

        style,
      ]}
      // accessibilityRole={getAccessibilityRole()}
      accessibilityLabel={`Heading level ${getHeadingLevel()}`}
      nativeID={id}
      {...rest}
    >
      {children}
    </RNText>
  );
};

export default Heading;
</file>

<file path="components/Hidden.tsx">
/**
 * Hidden component for the design system
 */

import React from 'react';
import { View, ViewProps, useWindowDimensions } from 'react-native';
import { breakpoints } from '../unistyles';

// Breakpoint values
type BreakpointValue = 'sm' | 'md' | 'lg' | 'xl';

// Hidden props
export interface HiddenProps extends ViewProps {
  /** Hide children above the passed breakpoint */
  above?: BreakpointValue;
  /** Hide children below the passed breakpoint */
  below?: BreakpointValue;
  /** Child components */
  children: React.ReactNode;
}

// Hidden component
export const Hidden: React.FC<HiddenProps> = ({
  above,
  below,
  children,
  style,
  ...rest
}) => {
  const { width } = useWindowDimensions();

  // Check if component should be hidden based on breakpoints
  const isHidden = (): boolean => {
    if (above) {
      const breakpoint = breakpoints[above];
      return width >= breakpoint;
    }

    if (below) {
      const breakpoint = breakpoints[below];
      return width < breakpoint;
    }

    return false;
  };

  // If hidden, don't render anything
  if (isHidden()) {
    return null;
  }

  // If the child is a React element, return it directly
  if (React.isValidElement(children)) {
    return children;
  }

  // Otherwise, wrap in a View
  return (
    <View style={style} {...rest}>
      {children}
    </View>
  );
};

export default Hidden;
</file>

<file path="components/IconButton.tsx">
/**
 * IconButton component for the design system
 */

import React from 'react';
import {
  GestureResponderEvent,
  Pressable,
  StyleSheet,
  TouchableOpacityProps,
  ViewStyle
} from 'react-native';
import tokens from '../tokens';
import VisuallyHidden from './VisuallyHidden';

// IconButton color variants
export type IconButtonColor = 'primary' | 'secondary' | 'inverted';

// IconButton size variants
export type IconButtonSize = 'regular' | 'large';

// IconButton props
export interface IconButtonProps extends TouchableOpacityProps {
  /** Accessible label for the button */
  label: string;
  /** URL to navigate to when pressed (makes it behave like a link) */
  href?: string;
  /** Whether the button is disabled */
  isDisabled?: boolean;
  /** Color variant of the button */
  color?: IconButtonColor;
  /** Size variant of the button */
  size?: IconButtonSize;
  /** Whether this is the current active item */
  isCurrent?: boolean;
  /** Function to call when the button is pressed */
  onPress?: (event: GestureResponderEvent) => void;
  /** Child components (should be an icon) */
  children: React.ReactNode;
}

// IconButton styles
const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderRadius: tokens.borders.radii.round,
  },
  regular: {
    minWidth: tokens.spacing.sizes.targetMinWidth,
    minHeight: tokens.spacing.sizes.targetMinHeight,
  },
  large: {
    minWidth: 48,
    minHeight: 48,
  },
  disabled: {
    opacity: 0.5,
  },
  primary: {
    // Default style
  },
  secondary: {
    // Secondary style
  },
  inverted: {
    // Inverted style
  },
  current: {
    // Current style
  },
  pressedPrimary: {
    backgroundColor: tokens.colors.neutralColors.neutral300,
  },
  pressedInverted: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  hoverPrimary: {
    backgroundColor: tokens.colors.backgroundColors.backgroundTertiary,
  },
  hoverInverted: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
});

// IconButton component
export const IconButton: React.FC<IconButtonProps> = ({
  label,
  href,
  isDisabled = false,
  color = 'primary',
  size = 'regular',
  isCurrent,
  onPress,
  style,
  children,
  ...rest
}) => {
  // Get size style
  const getSizeStyle = (): ViewStyle => {
    return size === 'large' ? styles.large : styles.regular;
  };

  // Get color style
  const getColorStyle = (): ViewStyle => {
    switch (color) {
      case 'secondary':
        return styles.secondary;
      case 'inverted':
        return styles.inverted;
      default:
        return styles.primary;
    }
  };

  // Get pressed style
  const getPressedStyle = (): ViewStyle => {
    return color === 'inverted' ? styles.pressedInverted : styles.pressedPrimary;
  };

  // Get hover style (for web)
  const getHoverStyle = (): ViewStyle => {
    return color === 'inverted' ? styles.hoverInverted : styles.hoverPrimary;
  };

  // Render the button
  return (
    <Pressable
      disabled={isDisabled}
      onPress={onPress}
      accessibilityLabel={label}
      accessibilityRole={href ? 'link' : 'button'}
      accessibilityState={{
        disabled: isDisabled,
        selected: isCurrent,
      }}
      style={({ pressed }) => [
        styles.button,
        getSizeStyle(),
        getColorStyle(),
        isDisabled && styles.disabled,
        isCurrent && styles.current,
        pressed && getPressedStyle(),
        style,
      ]}
      {...rest}
    >
      <VisuallyHidden>{label}</VisuallyHidden>
      {children}
    </Pressable>
  );
};

export default IconButton;
</file>

<file path="components/Image.tsx">
/**
 * Image component for the design system
 */

import React, { useEffect, useState } from 'react';
import {
  ImageStyle,
  Image as RNImage,
  ImageProps as RNImageProps,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import tokens from '../tokens';

// Object fit values
type ObjectFitValue = 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';

// Object position values
type ObjectPositionValue = 'bottom' | 'center' | 'left' | 'right' | 'top';

// Image props
export interface ImageProps extends Omit<RNImageProps, 'source'> {
  /** Alternative text for the image */
  alt: string;
  /** Aspect ratio of the image (e.g., '16/9') */
  aspectRatio?: string;
  /** Border radius of the image */
  borderRadius?: number;
  /** Whether to lazy load the image */
  hasLazyLoad?: boolean;
  /** Height of the image */
  height?: number;
  /** Maximum height of the image */
  maxHeight?: number;
  /** Maximum width of the image */
  maxWidth?: number;
  /** Minimum height of the image */
  minHeight?: number;
  /** Minimum width of the image */
  minWidth?: number;
  /** How the image should fit within its container */
  objectFit?: ObjectFitValue;
  /** Position of the image within its container */
  objectPosition?: ObjectPositionValue;
  /** Source URL of the image */
  src: string;
  /** Fallback source URL if the main source fails to load */
  fallbackSrc?: string;
  /** Width of the image */
  width?: number;
  /** Custom style for the image */
  style?: ImageStyle;
}

// Image styles
const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  rounded: {
    borderRadius: tokens.borders.radii.m,
  },
  square: {
    borderRadius: 0,
  },
});

// Helper function to get aspect ratio dimensions
const getAspectRatioDimensions = (
  aspectRatio?: string
): { width: number; height: number } | undefined => {
  if (!aspectRatio) return undefined;

  const [widthStr, heightStr] = aspectRatio.split('/');
  const width = parseFloat(widthStr);
  const height = parseFloat(heightStr);

  if (isNaN(width) || isNaN(height)) return undefined;

  return { width, height };
};

// Image component
export const Image: React.FC<ImageProps> = ({
  alt,
  aspectRatio,
  borderRadius = 'square',
  hasLazyLoad,
  height,
  maxHeight,
  maxWidth,
  minHeight,
  minWidth,
  objectFit = 'cover',
  objectPosition,
  src,
  fallbackSrc,
  width,
  style,
  ...rest
}) => {
  const [hasError, setHasError] = useState(false);
  const [imageSource, setImageSource] = useState({ uri: src });

  // Reset error state when src changes
  useEffect(() => {
    setHasError(false);
    setImageSource({ uri: src });
  }, [src]);

  // Handle image load error
  const handleError = () => {
    if (fallbackSrc && !hasError) {
      setHasError(true);
      setImageSource({ uri: fallbackSrc });
    }
  };

  // Get border radius style
  const getBorderRadiusStyle = (): ViewStyle => {
    if (borderRadius !== undefined) {
      return { borderRadius };
    }
    return {};
  };

  // Get object fit as resizeMode prop
  const getResizeMode = (): 'contain' | 'cover' | 'stretch' | 'center' => {
    switch (objectFit) {
      case 'contain':
        return 'contain';
      case 'cover':
        return 'cover';
      case 'fill':
        return 'stretch';
      case 'none':
        return 'center';
      case 'scale-down':
        return 'contain';
      default:
        return 'cover';
    }
  };

  // Get dimensions style
  const getDimensionsStyle = (): ViewStyle => {
    const dimensionsStyle: ViewStyle = {};

    if (width !== undefined) dimensionsStyle.width = width;
    if (height !== undefined) dimensionsStyle.height = height;
    if (minWidth !== undefined) dimensionsStyle.minWidth = minWidth;
    if (minHeight !== undefined) dimensionsStyle.minHeight = minHeight;
    if (maxWidth !== undefined) dimensionsStyle.maxWidth = maxWidth;
    if (maxHeight !== undefined) dimensionsStyle.maxHeight = maxHeight;

    // Handle aspect ratio
    const aspectRatioDimensions = getAspectRatioDimensions(aspectRatio);
    if (aspectRatioDimensions) {
      dimensionsStyle.aspectRatio = aspectRatioDimensions.width / aspectRatioDimensions.height;
    }

    return dimensionsStyle;
  };

  return (
    <View style={[styles.container, getBorderRadiusStyle(), getDimensionsStyle()]}>
      <RNImage
        source={imageSource}
        accessibilityLabel={alt}
        onError={handleError}
        resizeMode={getResizeMode()}
        style={[styles.image, style]}
        {...rest}
      />
    </View>
  );
};

export default Image;
</file>

<file path="components/PageGrid.tsx">
/**
 * PageGrid component for the design system
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import tokens from '../tokens';

// Grid data (source of truth for the PageGrid)
export const gridData = {
  columns: {
    initial: 9,
    md: 12,
  },
  gaps: {
    initial: tokens.spacing.space[2],
    md: tokens.spacing.space[3],
    lg: tokens.spacing.space[4],
    xl: tokens.spacing.space[6],
  },
  gutters: {
    initial: tokens.spacing.space[6],
    md: tokens.spacing.space[10],
    xl: tokens.spacing.space[24],
  },
  maxWidth: {
    initial: '100%',
    xl: 1440,
  },
};

// PageGrid props
export interface PageGridProps extends ViewProps {
  /** Grid template areas */
  gridTemplateAreas?: string | Record<string, string>;
  /** Grid template rows */
  gridTemplateRows?: string;
  /** Child components */
  children: React.ReactNode;
}

// PageGrid item props
export interface PageGridItemProps extends ViewProps {
  /** Grid area name */
  gridArea?: string;
  /** Grid column start */
  gridColumnStart?: string | number;
  /** Grid column end */
  gridColumnEnd?: string | number;
  /** Grid column span */
  gridColumn?: string | Record<string, string>;
  /** Grid row start */
  gridRowStart?: string | number;
  /** Grid row end */
  gridRowEnd?: string | number;
  /** Grid row span */
  gridRow?: string;
  /** Order of the grid item */
  order?: number;
  /** Child components */
  children: React.ReactNode;
}

// PageGrid styles
const styles = StyleSheet.create({
  pageGrid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginLeft: 'auto',
    marginRight: 'auto',
    paddingHorizontal: gridData.gutters.initial,
  },
  pageGridItem: {
    flexGrow: 0,
    flexShrink: 0,
  },
});

// PageGridItem component
const PageGridItem: React.FC<PageGridItemProps> = ({
  gridArea,
  gridColumnStart,
  gridColumnEnd,
  gridColumn,
  gridRowStart,
  gridRowEnd,
  gridRow,
  order,
  style,
  children,
  ...rest
}) => {
  // Parse grid column value
  const parseGridColumn = (): ViewStyle => {
    if (!gridColumn) return {};

    // Handle responsive gridColumn
    if (typeof gridColumn === 'object') {
      // For simplicity, we'll just use the 'initial' value
      const initialValue = gridColumn.initial;
      if (initialValue) {
        return parseGridColumnString(initialValue);
      }
      return {};
    }

    return parseGridColumnString(gridColumn);
  };

  // Parse grid column string
  const parseGridColumnString = (columnStr: string): ViewStyle => {
    // Handle full width
    if (columnStr === '1/-1' || columnStr === '1 / -1') {
      return { width: '100%' };
    }

    // Handle specific column spans
    const parts = columnStr.split('/').map(p => p.trim());
    if (parts.length === 2) {
      const start = parseInt(parts[0], 10);
      const end = parseInt(parts[1], 10);

      if (!isNaN(start) && !isNaN(end)) {
        const totalColumns = gridData.columns.initial;
        const span = end > 0 ? end - start : totalColumns + end - start;
        return { width: `${(span / totalColumns) * 100}%` };
      }
    }

    return {};
  };

  // Get column span from start/end
  const getColumnSpan = (): ViewStyle => {
    if (gridColumnStart && gridColumnEnd) {
      const start = parseInt(String(gridColumnStart), 10);
      const end = parseInt(String(gridColumnEnd), 10);

      if (!isNaN(start) && !isNaN(end)) {
        const totalColumns = gridData.columns.initial;
        const span = end > 0 ? end - start : totalColumns + end - start;
        return { width: `${(span / totalColumns) * 100}%` };
      }
    }

    return {};
  };

  // Create style object
  const itemStyle: ViewStyle = {
    ...(order !== undefined && { order }),
    ...parseGridColumn(),
    ...getColumnSpan(),
  };

  return (
    <View style={[styles.pageGridItem, itemStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// PageGrid component
const PageGridComponent: React.FC<PageGridProps> = ({
  gridTemplateAreas,
  gridTemplateRows,
  style,
  children,
  ...rest
}) => {
  // Get gap based on screen width
  const getGap = (): number => {
    // In a real implementation, this would use a hook to get the current screen width
    // and return the appropriate gap value
    return gridData.gaps.initial;
  };

  // Get gutters based on screen width
  const getGutters = (): number => {
    // In a real implementation, this would use a hook to get the current screen width
    // and return the appropriate gutter value
    return gridData.gutters.initial;
  };

  // Get max width based on screen width
  const getMaxWidth = (): number => {
    // In a real implementation, this would use a hook to get the current screen width
    // and return the appropriate max width value
    const maxWidth = gridData.maxWidth.initial;
    // Convert string percentage to number if needed
    if (typeof maxWidth === 'string' && maxWidth.endsWith('%')) {
      return parseFloat(maxWidth) / 100;
    }
    return typeof maxWidth === 'number' ? maxWidth : 1;
  };

  // Create style object
  const gridStyle: ViewStyle = {
    gap: getGap(),
    paddingHorizontal: getGutters(),
    maxWidth: getMaxWidth(),
  };

  return (
    <View style={[styles.pageGrid, gridStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// Add PageGridItem to PageGrid component
type PageGridComponentType = typeof PageGridComponent & {
  Item: typeof PageGridItem;
};

// Create PageGrid with PageGridItem
const PageGrid = PageGridComponent as PageGridComponentType;
PageGrid.Item = PageGridItem;

// Display names
PageGridComponent.displayName = 'PageGrid';
PageGridItem.displayName = 'PageGrid.Item';

export default PageGrid;
</file>

<file path="components/Skeleton.tsx">
/**
 * Skeleton component for the design system
 */

import React from 'react';
import { Animated, StyleSheet, View, ViewProps, ViewStyle } from 'react-native';
import tokens from '../tokens';

// Skeleton variant types
export type SkeletonVariant = 'text' | 'circle' | 'rectangular';

// Skeleton props
export interface SkeletonProps extends ViewProps {
  /** The type of skeleton to display */
  variant?: SkeletonVariant;
  /** Width of the skeleton */
  width?: number | string;
  /** Height of the skeleton */
  height?: number | string;
  /** Child components (will be hidden) */
  children?: React.ReactNode;
}

// Skeleton styles
const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: tokens.colors.backgroundColors.backgroundTertiary,
    borderRadius: tokens.borders.radii.m,
    overflow: 'hidden',
  },
  circle: {
    borderRadius: tokens.borders.radii.round,
  },
  rectangular: {
    borderRadius: 0,
  },
  text: {
    height: 'auto',
    transform: [{ scaleY: 0.8 }],
  },
  childContainer: {
    opacity: 0,
  },
});

// Skeleton component
export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'text',
  width,
  height,
  style,
  children,
  ...rest
}) => {
  // Create animation value
  const opacity = React.useRef(new Animated.Value(1)).current;

  // Start animation on mount
  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.5,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [opacity]);

  // Get variant style
  const getVariantStyle = (): ViewStyle => {
    switch (variant) {
      case 'circle':
        return styles.circle;
      case 'rectangular':
        return styles.rectangular;
      case 'text':
      default:
        return styles.text;
    }
  };

  // Get dimensions style
  const getDimensionsStyle = (): ViewStyle => {
    const dimensionsStyle: ViewStyle = {};

    if (width !== undefined) {
      dimensionsStyle.width = typeof width === 'string' ? parseInt(width, 10) : width;
    }
    if (height !== undefined) {
      dimensionsStyle.height = typeof height === 'string' ? parseInt(height, 10) : height;
    }

    return dimensionsStyle;
  };

  // If children are provided, measure them to get dimensions
  const childrenRef = React.useRef<View>(null);
  const [childDimensions, setChildDimensions] = React.useState<{ width?: number; height?: number }>({});

  const onChildLayout = React.useCallback((event: { nativeEvent: { layout: { width: number, height: number } } }) => {
    const { width, height } = event.nativeEvent.layout;
    setChildDimensions({ width, height });
  }, []);

  // Create combined style
  const combinedStyle = [
    styles.skeleton,
    getVariantStyle(),
    getDimensionsStyle(),
    children ? childDimensions : {},
    style,
  ];

  return (
    <Animated.View
      style={[combinedStyle, { opacity }]}
      {...rest}
    >
      {children && (
        <View
          ref={childrenRef}
          style={styles.childContainer}
          onLayout={onChildLayout}
          accessibilityElementsHidden={true}
          importantForAccessibility="no"
        >
          {children}
        </View>
      )}
    </Animated.View>
  );
};

export default Skeleton;
</file>

<file path="components/Stack.tsx">
/**
 * Stack component for the design system
 */

import React from 'react';
import { StyleSheet, View, ViewProps, ViewStyle } from 'react-native';
import tokens from '../tokens';

// Stack direction values
export type StackDirection = 'row' | 'column';

// Stack alignment values
export type StackAlignment = 'start' | 'center' | 'end' | 'justify';

// Stack gap values
export type StackGap = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '10' | '12' | '16';

// Stack props
export interface StackProps extends ViewProps {
  /** Direction of the stack */
  direction?: StackDirection | Record<string, StackDirection>;
  /** Gap between stack items */
  gap?: StackGap | Record<string, StackGap>;
  /** Whether the stack should wrap */
  wrap?: boolean;
  /** Horizontal alignment of stack items */
  alignX?: StackAlignment;
  /** Vertical alignment of stack items */
  alignY?: StackAlignment;
  /** Whether the stack should be displayed inline */
  inline?: boolean;
  /** Child components */
  children: React.ReactNode;
}

// Stack item props
export interface StackItemProps extends ViewProps {
  /** Whether the item should grow to fill available space */
  grow?: boolean | Record<string, boolean>;
  /** Whether the item should shrink to fit available space */
  shrink?: boolean | Record<string, boolean>;
  /** Child components */
  children: React.ReactNode;
}

// Stack styles
const styles = StyleSheet.create({
  stack: {
    display: 'flex',
    flexWrap: 'nowrap',
  },
  row: {
    flexDirection: 'row',
  },
  column: {
    flexDirection: 'column',
  },
  wrap: {
    flexWrap: 'wrap',
  },
  noWrap: {
    flexWrap: 'nowrap',
  },
  inline: {
    // React Native doesn't support inline-flex, so we use flex
    display: 'flex',
  },
  stackItem: {
    flexGrow: 0,
  },
  grow: {
    flexGrow: 1,
    flexBasis: 0,
  },
  noGrow: {
    flexGrow: 0,
  },
  shrink: {
    flexShrink: 1,
  },
  noShrink: {
    flexShrink: 0,
  },
});

// StackItem component
const StackItem: React.FC<StackItemProps> = ({
  grow = false,
  shrink = true,
  style,
  children,
  ...rest
}) => {
  // Get grow style
  const getGrowStyle = (): ViewStyle => {
    if (typeof grow === 'object') {
      // For simplicity, we'll just use the 'initial' value
      return grow.initial ? styles.grow : styles.noGrow;
    }
    return grow ? styles.grow : styles.noGrow;
  };

  // Get shrink style
  const getShrinkStyle = (): ViewStyle => {
    if (typeof shrink === 'object') {
      // For simplicity, we'll just use the 'initial' value
      return shrink.initial ? styles.shrink : styles.noShrink;
    }
    return shrink ? styles.shrink : styles.noShrink;
  };

  return (
    <View style={[styles.stackItem, getGrowStyle(), getShrinkStyle(), style]} {...rest}>
      {children}
    </View>
  );
};

// Stack component
const StackComponent: React.FC<StackProps> = ({
  direction = 'column',
  gap = '0',
  wrap = false,
  alignX,
  alignY,
  inline,
  style,
  children,
  ...rest
}) => {
  // Get direction style
  const getDirectionStyle = (): ViewStyle => {
    if (typeof direction === 'object') {
      // For simplicity, we'll just use the 'initial' value
      return direction.initial === 'row' ? styles.row : styles.column;
    }
    return direction === 'row' ? styles.row : styles.column;
  };

  // Get gap value
  const getGapValue = (): number => {
    const gapValue = typeof gap === 'object' ? gap.initial : gap;
    return gapValue ? tokens.spacing.space[gapValue] : 0;
  };

  // Get wrap style
  const getWrapStyle = (): ViewStyle => {
    return wrap ? styles.wrap : styles.noWrap;
  };

  // Get alignment styles
  const getAlignmentStyles = (): ViewStyle => {
    const isRow = typeof direction === 'object'
      ? direction.initial === 'row'
      : direction === 'row';

    const alignmentStyle: ViewStyle = {};

    // Handle alignX
    if (alignX) {
      if (isRow) {
        // For row direction, alignX affects justifyContent
        switch (alignX) {
          case 'start': alignmentStyle.justifyContent = 'flex-start'; break;
          case 'center': alignmentStyle.justifyContent = 'center'; break;
          case 'end': alignmentStyle.justifyContent = 'flex-end'; break;
          case 'justify': alignmentStyle.justifyContent = 'space-between'; break;
        }
      } else {
        // For column direction, alignX affects alignItems
        switch (alignX) {
          case 'start': alignmentStyle.alignItems = 'flex-start'; break;
          case 'center': alignmentStyle.alignItems = 'center'; break;
          case 'end': alignmentStyle.alignItems = 'flex-end'; break;
          case 'justify': alignmentStyle.alignItems = 'stretch'; break;
        }
      }
    }

    // Handle alignY
    if (alignY) {
      if (isRow) {
        // For row direction, alignY affects alignItems
        switch (alignY) {
          case 'start': alignmentStyle.alignItems = 'flex-start'; break;
          case 'center': alignmentStyle.alignItems = 'center'; break;
          case 'end': alignmentStyle.alignItems = 'flex-end'; break;
          case 'justify': alignmentStyle.alignItems = 'stretch'; break;
        }
      } else {
        // For column direction, alignY affects justifyContent
        switch (alignY) {
          case 'start': alignmentStyle.justifyContent = 'flex-start'; break;
          case 'center': alignmentStyle.justifyContent = 'center'; break;
          case 'end': alignmentStyle.justifyContent = 'flex-end'; break;
          case 'justify': alignmentStyle.justifyContent = 'space-between'; break;
        }
      }
    }

    return alignmentStyle;
  };

  return (
    <View
      style={[
        styles.stack,
        getDirectionStyle(),
        getWrapStyle(),
        { gap: getGapValue() },
        getAlignmentStyles(),
        inline && styles.inline,
        style,
      ]}
      {...rest}
    >
      {children}
    </View>
  );
};

// Add StackItem to Stack component
type StackComponentType = typeof StackComponent & {
  Item: typeof StackItem;
};

// Create Stack with StackItem
const Stack = StackComponent as StackComponentType;
Stack.Item = StackItem;

// Display names
StackComponent.displayName = 'Stack';
StackItem.displayName = 'Stack.Item';

export default Stack;
</file>

<file path="components/Text.tsx">
/**
 * Text component for the design system
 */

import React from 'react';
import { Text as RNText, TextProps as RNTextProps, TextStyle } from 'react-native';
import { useAppTheme } from '../provider';
import { TypographyVariant } from '../theme';

// Text props
export interface TextProps extends RNTextProps {
  variant?: TypographyVariant;
  children: React.ReactNode;
  color?: string;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
}



// Text component
export const Text: React.FC<TextProps> = ({
  variant = 'bodyMedium',
  children,
  color,
  align,
  style,
  ...rest
}) => {
  const { theme } = useAppTheme();

  // Get typography style for the variant
  const variantStyle = theme.typography[variant] as TextStyle;

  // Determine the text color with proper priority
  const textColor = color || theme.colors.textPrimary;

  return (
    <RNText
      style={[
        variantStyle,
        { color: textColor }, // Apply determined color
        align && { textAlign: align },
        style, // Allow style prop to override everything except color
      ]}
      {...rest}
    >
      {children}
    </RNText>
  );
};

// Heading component
export interface HeadingProps extends Omit<TextProps, 'variant'> {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

export const Heading: React.FC<HeadingProps> = ({
  level = 1,
  children,
  ...rest
}) => {
  // Map heading level to variant
  const getVariant = (): TypographyVariant => {
    switch (level) {
      case 1:
        return 'displayLarge';
      case 2:
        return 'displayMedium';
      case 3:
        return 'displaySmall';
      case 4:
        return 'headlineLarge';
      case 5:
        return 'headlineMedium';
      case 6:
        return 'headlineSmall';
      default:
        return 'displayLarge';
    }
  };

  return (
    <Text variant={getVariant()} {...rest}>
      {children}
    </Text>
  );
};

// Paragraph component
export const Paragraph: React.FC<Omit<TextProps, 'variant'>> = (props) => {
  return <Text variant="bodyMedium" {...props} />;
};

// Caption component
export const Caption: React.FC<Omit<TextProps, 'variant'>> = (props) => {
  return <Text variant="bodySmall" {...props} />;
};

// Label component
export const Label: React.FC<Omit<TextProps, 'variant'>> = (props) => {
  return <Text variant="labelMedium" {...props} />;
};

// Export all components
export default {
  Text,
  Heading,
  Paragraph,
  Caption,
  Label,
};
</file>

<file path="components/TextLink.tsx">
/**
 * TextLink component for the design system
 */

import React from 'react';
import {
  GestureResponderEvent,
  Linking,
  StyleProp,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View
} from 'react-native';
import tokens from '../tokens';
import { Text } from './Text';

// TextLink emphasis variants
export type TextLinkEmphasis = 'low' | 'medium' | 'high';

// TextLink color types
export type TextLinkColor = string;

// TextLink props
export interface TextLinkProps {
  /** Text shown inside the link */
  children: React.ReactNode;
  /** Emphasis level of the link */
  emphasis?: TextLinkEmphasis;
  /** URL to navigate to when pressed */
  href?: string;
  /** Icon shown to the left of the text */
  leftIcon?: React.ReactNode;
  /** Icon shown to the right of the text */
  rightIcon?: React.ReactNode;
  /** Function to call when the link is pressed */
  onPress?: (event: GestureResponderEvent) => void;
  /** Target for the link (e.g., '_blank') */
  target?: string;
  /** Color of the link */
  color?: TextLinkColor;
  /** Accessible label for the link */
  ariaLabel?: string;
  /** Custom style for the link */
  style?: StyleProp<TextStyle>;
}

// TextLink styles
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    textDecorationLine: 'underline',
  },
  lowEmphasis: {
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
    textDecorationColor: 'currentColor',
  },
  mediumEmphasis: {
    fontWeight: 'bold',
    color: tokens.colors.linkColors.linkPrimary,
  },
  highEmphasis: {
    fontWeight: 'bold',
    color: tokens.colors.linkColors.linkBrand,
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
    textDecorationColor: tokens.colors.linkColors.linkBrand,
  },
  leftIconContainer: {
    marginRight: tokens.spacing.space[1],
  },
  rightIconContainer: {
    marginLeft: tokens.spacing.space[1],
  },
});

// TextLink component
export const TextLink: React.FC<TextLinkProps> = ({
  children,
  emphasis = 'low',
  href,
  leftIcon,
  rightIcon,
  onPress,
  target,
  color,
  ariaLabel,
  style,
}) => {
  // Handle press event
  const handlePress = (event: GestureResponderEvent) => {
    if (onPress) {
      onPress(event);
    } else if (href) {
      Linking.openURL(href);
    }
  };

  // Get emphasis style
  const getEmphasisStyle = (): TextStyle => {
    switch (emphasis) {
      case 'medium':
        return styles.mediumEmphasis;
      case 'high':
        return styles.highEmphasis;
      case 'low':
      default:
        return styles.lowEmphasis;
    }
  };

  // Get color style
  const getColorStyle = (): TextStyle => {
    if (!color) return {};
    
    return { color };
  };

  // Render the link content
  const renderLinkContent = () => {
    return (
      <View style={styles.container}>
        {leftIcon && <View style={styles.leftIconContainer}>{leftIcon}</View>}
        <Text
          style={[
            styles.text,
            getEmphasisStyle(),
            getColorStyle(),
            style,
          ]}
        >
          {children}
        </Text>
        {rightIcon && <View style={styles.rightIconContainer}>{rightIcon}</View>}
      </View>
    );
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      accessibilityRole="link"
      accessibilityLabel={ariaLabel || (typeof children === 'string' ? children : undefined)}
      accessibilityHint={target === '_blank' ? 'Opens in a new window' : undefined}
    >
      {renderLinkContent()}
    </TouchableOpacity>
  );
};

export default TextLink;
</file>

<file path="components/VisuallyHidden.tsx">
/**
 * VisuallyHidden component for the design system
 */

import React from 'react';
import { StyleSheet, View, ViewProps } from 'react-native';
import { Text } from './Text';

// VisuallyHidden props
export interface VisuallyHiddenProps extends ViewProps {
  /** Content to be visually hidden but accessible to screen readers */
  children: React.ReactNode;
  /** Optional ID for the element */
  id?: string;
  /** Optional ARIA live region setting */
  ariaLive?: 'polite' | 'assertive' | 'none';
}

// VisuallyHidden styles
const styles = StyleSheet.create({
  visuallyHidden: {
    position: 'absolute',
    width: 1,
    height: 1,
    padding: 0,
    margin: -1,
    overflow: 'hidden',
    // In React Native, we can't use clip, so we use opacity
    opacity: 0,
    // Position off-screen
    left: -10000,
    top: -10000,
  },
});

// VisuallyHidden component
export const VisuallyHidden: React.FC<VisuallyHiddenProps> = ({
  children,
  id,
  ariaLive,
  style,
  ...rest
}) => {
  return (
    <View
      style={[styles.visuallyHidden, style]}
      accessibilityElementsHidden={false}
      importantForAccessibility="yes"
      accessibilityLiveRegion={ariaLive}
      {...rest}
    >
      {typeof children === 'string' ? (
        <Text id={id}>{children}</Text>
      ) : (
        children
      )}
    </View>
  );
};

export default VisuallyHidden;
</file>

<file path="provider.tsx">
/**
 * Theme provider component for the design system
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { UnistylesRuntime } from 'react-native-unistyles';
import { AppTheme, themes } from './theme';

// Create theme context
type ThemeContextType = {
  theme: AppTheme;
  isDarkMode: boolean;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Custom hook to use theme
export const useAppTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context;
};

// Theme provider props
type ThemeProviderProps = {
  children: React.ReactNode;
};

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Always use light theme as requested
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Initialize theme on mount for web
  useEffect(() => {
    // Set initial theme for web
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

      // Apply theme colors directly to root element for better web compatibility
      if (isDarkMode) {
        // Dark theme
        document.documentElement.style.setProperty('--text-color', themes.dark.colors.textPrimary);
        document.documentElement.style.setProperty('--bg-color', themes.dark.colors.background);
      } else {
        // Light theme
        document.documentElement.style.setProperty('--text-color', themes.light.colors.textPrimary);
        document.documentElement.style.setProperty('--bg-color', themes.light.colors.background);
      }

      // Add global styles for scrolling
      const style = document.createElement('style');
      style.textContent = `
        html, body, #root {
          height: 100%;
          width: 100%;
          margin: 0;
          padding: 0;
          overflow-y: auto;
          color: var(--text-color);
          background-color: var(--bg-color);
        }
      `;
      document.head.appendChild(style);
    }
  }, [isDarkMode]);

  // Always use light theme regardless of device settings
  useEffect(() => {
    if (UnistylesRuntime.hasAdaptiveThemes) {
      // Force light theme
      setIsDarkMode(false);

      // Update web theme to always use light
      if (typeof document !== 'undefined') {
        document.documentElement.setAttribute('data-theme', 'light');
      }
    }
  }, []);

  // Toggle theme function
  const toggleTheme = () => {
    setIsDarkMode((prev) => {
      const newMode = !prev;

      // Update Unistyles theme
      UnistylesRuntime.setTheme(newMode ? 'dark' : 'light');

      // For web platform, also update the HTML element's data-theme attribute
      if (typeof document !== 'undefined') {
        document.documentElement.setAttribute('data-theme', newMode ? 'dark' : 'light');

        // Apply theme colors directly to root element for better web compatibility
        if (newMode) {
          // Dark theme
          document.documentElement.style.setProperty('--text-color', themes.dark.colors.textPrimary);
          document.documentElement.style.setProperty('--bg-color', themes.dark.colors.background);
        } else {
          // Light theme
          document.documentElement.style.setProperty('--text-color', themes.light.colors.textPrimary);
          document.documentElement.style.setProperty('--bg-color', themes.light.colors.background);
        }

        // Force a re-render on web by triggering a small resize event
        window.dispatchEvent(new Event('resize'));
      }

      return newMode;
    });
  };

  // Get current theme
  const theme = isDarkMode ? themes.dark : themes.light;

  // Context value
  const contextValue: ThemeContextType = {
    theme,
    isDarkMode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
</file>

</files>
