// Color Tokens - Design System Aligned with Eneco Brand
// Based on design system from repomix-output.xml

// Primary colors - Eneco Purple (Design System Compatible)
$primary-50: #f5f3ff;   // Lightest purple tint
$primary-100: #ede9fe;  // Very light purple (backgroundTertiary equivalent)
$primary-200: #ddd6fe;  // Light purple
$primary-300: #c4b5fd;  // Medium light purple (neutral300 equivalent)
$primary-400: #a78bfa;  // Medium purple (dark theme primary)
$primary-500: #8b5cf6;  // Base purple
$primary-600: #7c3aed;  // Medium dark purple (secondary brand)
$primary-700: #6d28d9;  // Dark purple (primary brand)
$primary-800: #5b21b6;  // Very dark purple
$primary-900: #4c1d95;  // Darkest purple (dark theme backgroundTertiary)

// Neutral/Gray colors (Design System neutralColors)
$neutral-white: #ffffff;    // neutralWhite
$neutral-50: #f9fafb;      // Almost white (backgroundTertiary)
$neutral-100: #f3f4f6;     // Very light gray (backgroundSecondary)
$neutral-200: #e5e7eb;     // Light gray (borders)
$neutral-300: #d1d5db;     // Medium light gray (neutral300)
$neutral-400: #9ca3af;     // Medium gray
$neutral-500: #6b7280;     // Base gray
$neutral-600: #4b5563;     // Medium dark gray
$neutral-700: #374151;     // Dark gray
$neutral-800: #1f2937;     // Very dark gray (dark theme surface)
$neutral-900: #111827;     // Almost black (textPrimary, neutral900)

// Legacy gray aliases for backward compatibility
$gray-50: $neutral-50;
$gray-100: $neutral-100;
$gray-200: $neutral-200;
$gray-300: $neutral-300;
$gray-400: $neutral-400;
$gray-500: $neutral-500;
$gray-600: $neutral-600;
$gray-700: $neutral-700;
$gray-800: $neutral-800;
$gray-900: $neutral-900;

// Success colors - Eneco Green (Design System green variants)
$success-50: #f0fdf4;   // Lightest green
$success-100: #dcfce7;  // Very light green (green100 equivalent)
$success-200: #bbf7d0;  // Light green
$success-300: #86efac;  // Medium light green
$success-400: #4ade80;  // Medium green
$success-500: #22c55e;  // Base green (Eneco brand green)
$success-600: #16a34a;  // Medium dark green (accentGreen700 equivalent)
$success-700: #15803d;  // Dark green
$success-800: #166534;  // Very dark green
$success-900: #14532d;  // Darkest green (green900 equivalent)

// Error colors (Design System pink variants)
$error-50: #fef2f2;     // Lightest red
$error-100: #fee2e2;    // Very light red (pink100 equivalent)
$error-200: #fecaca;    // Light red
$error-300: #fca5a5;    // Medium light red
$error-400: #f87171;    // Medium red
$error-500: #ef4444;    // Base red (error feedback)
$error-600: #dc2626;    // Medium dark red
$error-700: #b91c1c;    // Dark red
$error-800: #991b1b;    // Very dark red
$error-900: #7f1d1d;    // Darkest red (pink900 equivalent)

// Warning colors - Eneco Orange (Design System yellow variants)
$warning-50: #fff7ed;   // Lightest orange
$warning-100: #ffedd5;  // Very light orange (yellow100 equivalent)
$warning-200: #fed7aa;  // Light orange
$warning-300: #fdba74;  // Medium light orange
$warning-400: #fb923c;  // Medium orange
$warning-500: #f97316;  // Base orange (Eneco brand orange)
$warning-600: #ea580c;  // Medium dark orange
$warning-700: #c2410c;  // Dark orange
$warning-800: #9a3412;  // Very dark orange
$warning-900: #7c2d12;  // Darkest orange (yellow900 equivalent)

// Design System Color Mappings
// These map directly to the design system theme.colors and tokens.colors structure

// Background Colors (theme.colors.background*)
$background-primary: $neutral-white;        // backgroundPrimary
$background-secondary: $neutral-100;        // backgroundSecondary
$background-tertiary: $neutral-50;          // backgroundTertiary
$background-scrim: rgba($neutral-900, 0.5); // backgroundScrim (overlay)
$background-dark: $neutral-900;             // backgroundDark
$background-pressed: $neutral-200;          // backgroundPressed

// Text Colors (theme.colors.text*, tokens.colors.textColors)
$text-primary: $neutral-900;                // textPrimary
$text-secondary: $neutral-600;              // textSecondary
$text-muted: $neutral-400;                  // textMuted
$text-brand: $primary-700;                  // textBrand
$text-on-primary: $neutral-white;           // onPrimary
$text-on-background-var-five: $neutral-700; // textOnBackgroundVarFive

// Brand Colors (theme.colors.primary/secondary)
$brand-primary: $primary-700;               // primary (main brand color)
$brand-secondary: $primary-600;             // secondary
$accent-green: $success-600;                // secondaryColors.accentGreen700

// Feedback Background Colors (theme.colors.feedbackBackground*)
$feedback-bg-success: $success-100;         // feedbackBackgroundSuccess
$feedback-bg-error: $error-100;             // feedbackBackgroundError
$feedback-bg-warning: $warning-100;         // feedbackBackgroundWarning
$feedback-bg-info: $primary-100;            // feedbackBackgroundInfo

// Link Colors (tokens.colors.linkColors)
$link-primary: $primary-600;                // linkPrimary
$link-brand: $primary-700;                  // linkBrand

// Eneco Brand Colors (Legacy - mapped to design system)
$eneco-purple-primary: $brand-primary;      // $primary-700
$eneco-purple-secondary: $brand-secondary;  // $primary-600
$eneco-purple-light: $primary-100;          // Light purple
$eneco-green: $success-500;                 // Eneco green
$eneco-orange: $warning-500;                // Eneco orange
$eneco-white: $neutral-white;               // White
$eneco-light-gray: #f8fafc;                 // Custom light gray (slightly warmer than neutral-50)

// Chat specific colors - Design System Aligned
$chat-user-bg: $brand-primary;              // User message background (primary)
$chat-agent-bg: $background-tertiary;       // Agent message background (backgroundTertiary)
$chat-user-text: $text-on-primary;          // User message text (onPrimary)
$chat-agent-text: $text-primary;            // Agent message text (textPrimary)
$chat-header-bg: $brand-primary;            // Chat header background
$chat-accent: $warning-500;                 // Chat accent color

// Connection status colors
$status-connected: $success-500;            // Connected state
$status-connecting: $warning-500;           // Connecting state
$status-disconnected: $neutral-400;         // Disconnected state
$status-error: $error-500;                  // Error state

// CSS Custom Properties - Design System Aligned
:root {
  // Light theme (default) - Design System Colors

  // Background Colors (theme.colors.background*)
  --color-background: #{$eneco-light-gray};           // Main app background
  --color-background-primary: #{$background-primary}; // backgroundPrimary (white)
  --color-background-secondary: #{$background-secondary}; // backgroundSecondary
  --color-background-tertiary: #{$background-tertiary}; // backgroundTertiary
  --color-background-scrim: #{$background-scrim};     // backgroundScrim (overlays)
  --color-background-dark: #{$background-dark};       // backgroundDark
  --color-background-pressed: #{$background-pressed}; // backgroundPressed

  // Surface Colors (legacy compatibility)
  --color-surface: #{$background-primary};            // Primary surface (white)
  --color-surface-secondary: #{$background-secondary}; // Secondary surface

  // Border Colors
  --color-border: #{$neutral-200};                    // Default borders
  --color-border-hover: #{$neutral-300};              // Hover state borders

  // Text Colors (theme.colors.text*, tokens.colors.textColors)
  --color-text-primary: #{$text-primary};             // textPrimary
  --color-text-secondary: #{$text-secondary};         // textSecondary
  --color-text-muted: #{$text-muted};                 // textMuted
  --color-text-brand: #{$text-brand};                 // textBrand
  --color-text-on-primary: #{$text-on-primary};       // onPrimary (text on primary bg)

  // Brand Colors (theme.colors.primary/secondary)
  --color-primary: #{$brand-primary};                 // primary brand color
  --color-primary-hover: #{$primary-800};             // primary hover state
  --color-primary-light: #{$primary-100};             // light primary variant
  --color-secondary: #{$brand-secondary};             // secondary brand color

  // Feedback Colors
  --color-success: #{$success-500};                   // Success state
  --color-error: #{$error-500};                       // Error state
  --color-warning: #{$warning-500};                   // Warning state
  --color-info: #{$primary-500};                      // Info state

  // Feedback Background Colors (theme.colors.feedbackBackground*)
  --color-feedback-bg-success: #{$feedback-bg-success}; // feedbackBackgroundSuccess
  --color-feedback-bg-error: #{$feedback-bg-error};   // feedbackBackgroundError
  --color-feedback-bg-warning: #{$feedback-bg-warning}; // feedbackBackgroundWarning
  --color-feedback-bg-info: #{$feedback-bg-info};     // feedbackBackgroundInfo

  // Link Colors (tokens.colors.linkColors)
  --color-link-primary: #{$link-primary};             // linkPrimary
  --color-link-brand: #{$link-brand};                 // linkBrand

  // Legacy Eneco brand colors (for backward compatibility)
  --color-eneco-purple: #{$eneco-purple-primary};
  --color-eneco-green: #{$eneco-green};
  --color-eneco-orange: #{$eneco-orange};

  // Chat colors (design system aligned)
  --color-chat-user-bg: #{$chat-user-bg};             // User message background
  --color-chat-agent-bg: #{$chat-agent-bg};           // Agent message background
  --color-chat-user-text: #{$chat-user-text};         // User message text
  --color-chat-agent-text: #{$chat-agent-text};       // Agent message text
  --color-chat-header-bg: #{$chat-header-bg};         // Chat header background
  --color-chat-accent: #{$chat-accent};               // Chat accent color

  // Status colors
  --color-status-connected: #{$status-connected};     // Connected state
  --color-status-connecting: #{$status-connecting};   // Connecting state
  --color-status-disconnected: #{$status-disconnected}; // Disconnected state
  --color-status-error: #{$status-error};             // Error state
}

// Dark theme - Design System Aligned
[data-theme="dark"] {
  // Background Colors (dark theme variants)
  --color-background: #{$neutral-900};                // Main app background (dark)
  --color-background-primary: #{$neutral-800};        // backgroundPrimary (dark surface)
  --color-background-secondary: #{$neutral-700};      // backgroundSecondary (dark)
  --color-background-tertiary: #{$neutral-600};       // backgroundTertiary (dark)
  --color-background-scrim: rgba(#{$neutral-900}, 0.8); // backgroundScrim (darker overlay)
  --color-background-dark: #{$neutral-900};           // backgroundDark
  --color-background-pressed: #{$neutral-600};        // backgroundPressed (dark)

  // Surface Colors (dark theme)
  --color-surface: #{$neutral-800};                   // Primary surface (dark)
  --color-surface-secondary: #{$neutral-700};         // Secondary surface (dark)

  // Border Colors (dark theme)
  --color-border: #{$neutral-600};                    // Default borders (dark)
  --color-border-hover: #{$neutral-500};              // Hover state borders (dark)

  // Text Colors (dark theme - inverted)
  --color-text-primary: #{$neutral-100};              // textPrimary (light on dark)
  --color-text-secondary: #{$neutral-300};            // textSecondary (light on dark)
  --color-text-muted: #{$neutral-500};                // textMuted (medium on dark)
  --color-text-brand: #{$primary-400};                // textBrand (lighter primary)
  --color-text-on-primary: #{$neutral-white};         // onPrimary (white on primary)

  // Brand Colors (dark theme - lighter variants)
  --color-primary: #{$primary-400};                   // primary (lighter for dark theme)
  --color-primary-hover: #{$primary-300};             // primary hover (even lighter)
  --color-primary-light: #{$primary-900};             // light primary (darker in dark theme)
  --color-secondary: #{$primary-500};                 // secondary (lighter variant)

  // Feedback Colors (dark theme - lighter variants)
  --color-success: #{$success-400};                   // Success (lighter green)
  --color-error: #{$error-400};                       // Error (lighter red)
  --color-warning: #{$warning-400};                   // Warning (lighter orange)
  --color-info: #{$primary-400};                      // Info (lighter blue)

  // Feedback Background Colors (dark theme)
  --color-feedback-bg-success: #{$success-900};       // Success background (dark green)
  --color-feedback-bg-error: #{$error-900};           // Error background (dark red)
  --color-feedback-bg-warning: #{$warning-900};       // Warning background (dark orange)
  --color-feedback-bg-info: #{$primary-900};          // Info background (dark blue)

  // Link Colors (dark theme)
  --color-link-primary: #{$primary-400};              // linkPrimary (lighter)
  --color-link-brand: #{$primary-300};                // linkBrand (even lighter)

  // Chat colors (dark theme optimized)
  --color-chat-user-bg: #{$primary-600};              // User message (keep primary-600)
  --color-chat-agent-bg: #{$neutral-700};             // Agent message (dark surface)
  --color-chat-user-text: #{$neutral-white};          // User message text (white)
  --color-chat-agent-text: #{$neutral-100};           // Agent message text (light)
  --color-chat-header-bg: #{$primary-700};            // Chat header (darker primary)
  --color-chat-accent: #{$warning-400};               // Chat accent (lighter orange)

  // Status colors (dark theme)
  --color-status-connected: #{$success-400};          // Connected (lighter green)
  --color-status-connecting: #{$warning-400};         // Connecting (lighter orange)
  --color-status-disconnected: #{$neutral-500};       // Disconnected (medium gray)
  --color-status-error: #{$error-400};                // Error (lighter red)
}
