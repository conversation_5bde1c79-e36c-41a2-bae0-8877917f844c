// Color Tokens - Design System Aligned with Eneco Brand
// Based on actual design system colors from repomix-output.xml colors.ts

// Brand Colors - Eneco Red/Orange (Primary Brand Identity)
$brand-red: #E5384C;        // brandRed - Primary Eneco brand color
$brand-orange: #EA714F;     // brandOrange - Secondary Eneco brand color
$brand-dark-red: #D21242;   // brandDarkRed - Dark variant
$brand-light-red: #F9C7CC;  // brandLightRed - Light variant

// Eneco Red Variants (for comprehensive brand palette)
$eneco-red-600: #e5384c;    // enecoRed600 - Base Eneco red
$eneco-red-700: #d21242;    // enecoRed700 - Dark Eneco red
$eneco-red-800: #bf0639;    // enecoRed800 - Very dark Eneco red
$eneco-red-900: #821034;    // enecoRed900 - Darkest Eneco red

// Orange Variants (complementary to brand red)
$orange-100: #ffe7dc;       // orange100 - Light orange
$orange-300: #ffba8f;       // orange300 - Medium light orange
$orange-400: #ff9363;       // orange400 - Medium orange
$orange-500: #ea714f;       // orange500 - Base orange (brand orange)

// Neutral Colors (Design System neutralColors - exact matches)
$neutral-white: #FFF;       // neutralWhite - Pure white
$neutral-50: #FCFAFA;       // neutral50 - Almost white
$neutral-100: #F8F6F6;      // neutral100 - Very light gray (backgroundSecondary)
$neutral-300: #F3F0F0;      // neutral300 - Light gray (backgroundTertiary)
$neutral-400: #DFDCDC;      // neutral400 - Medium light gray (borders)
$neutral-800: #716A6A;      // neutral800 - Medium dark gray (low emphasis text)
$neutral-900: #2F2D2D;      // neutral900 - Very dark gray (textPrimary)
$neutral-black: #000;       // neutralBlack - Pure black

// Opacity Colors (Design System opacityColors)
$white-opacity-15: #{$neutral-white}15;  // whiteOpacity15
$white-opacity-30: #{$neutral-white}30;  // whiteOpacity30
$black-opacity-40: #00000040;            // blackOpacity40
$black-opacity-70: #{$neutral-900}70;    // blackOpacity70

// Legacy gray aliases for backward compatibility
$gray-50: $neutral-50;
$gray-100: $neutral-100;
$gray-200: $neutral-200;
$gray-300: $neutral-300;
$gray-400: $neutral-400;
$gray-500: $neutral-500;
$gray-600: $neutral-600;
$gray-700: $neutral-700;
$gray-800: $neutral-800;
$gray-900: $neutral-900;

// Green Colors (Design System secondaryColors.green* and accentGreen*)
$green-50: #F2F7EC;         // green50 - Lightest green
$green-100: #E4EFD8;        // green100 - Very light green
$green-300: #CDE3BB;        // green300 - Light green
$green-500: #7EC389;        // green500 - Medium green
$green-700: #009b65;        // green700 - Dark green (primary functional)
$green-800: #2C6F49;        // green800 - Very dark green
$green-900: #0A4033;        // green900 - Darkest green

// Accent Green Colors (Key functional colors)
$accent-green-100: #e3faea; // accentGreen100 - Light accent green
$accent-green-200: #c0eaca; // accentGreen200 - Medium light accent green
$accent-green-300: #84dc99; // accentGreen300 - Medium accent green
$accent-green-600: #009b65; // accentGreen600 - Primary accent green
$accent-green-700: #007250; // accentGreen700 - Dark accent green (textBrand)
$accent-green-800: #00593f; // accentGreen800 - Very dark accent green

// Success colors (mapped to green variants for semantic use)
$success-50: $green-50;     // Lightest success
$success-100: $green-100;   // Very light success
$success-300: $green-300;   // Light success
$success-500: $green-500;   // Base success
$success-600: $accent-green-600; // Medium dark success (primary functional)
$success-700: $accent-green-700; // Dark success (text brand)
$success-800: $green-800;   // Very dark success
$success-900: $green-900;   // Darkest success

// Error colors (Design System brand colors - using Eneco red for errors)
$error-light: $brand-light-red;  // #F9C7CC - Light error background
$error-base: $brand-red;         // #E5384C - Base error (brandRed)
$error-dark: $brand-dark-red;    // #D21242 - Dark error (brandDarkRed)
$error-darker: $eneco-red-800;   // #bf0639 - Very dark error
$error-darkest: $eneco-red-900;  // #821034 - Darkest error

// Legacy error color mappings for backward compatibility
$error-50: $brand-light-red;     // Light error background
$error-100: $brand-light-red;    // Very light error
$error-500: $error-base;         // Base error
$error-600: $error-dark;         // Medium dark error
$error-700: $error-darker;       // Dark error
$error-800: $error-darker;       // Very dark error
$error-900: $error-darkest;      // Darkest error

// Warning colors (Design System orange variants)
$warning-100: $orange-100;       // #ffe7dc - Light warning background
$warning-300: $orange-300;       // #ffba8f - Medium light warning
$warning-400: $orange-400;       // #ff9363 - Medium warning
$warning-500: $orange-500;       // #ea714f - Base warning (brand orange)

// Legacy warning color mappings for backward compatibility
$warning-50: $orange-100;        // Lightest warning
$warning-600: $brand-orange;     // Medium dark warning
$warning-700: $brand-orange;     // Dark warning
$warning-800: $brand-orange;     // Very dark warning
$warning-900: $brand-orange;     // Darkest warning

// Design System Semantic Color Mappings
// These map directly to the design system semantic colors from colors.ts

// Background Colors (backgroundColors from design system)
$background-primary: $neutral-white;        // backgroundPrimary: neutralWhite
$background-secondary: $neutral-100;        // backgroundSecondary: neutral100
$background-tertiary: $neutral-300;         // backgroundTertiary: neutral300
$background-scrim: $black-opacity-70;       // backgroundScrim: blackOpacity70
$background-dark: $neutral-900;             // backgroundDark: neutral900
$background-pressed: $neutral-100;          // backgroundPressed: neutral100

// Text Colors (textColors from design system)
$text-primary: $neutral-900;                // textPrimary: neutral900
$text-inverted: $neutral-white;             // textInverted: neutralWhite
$text-brand: $accent-green-700;             // textBrand: accentGreen700
$text-low-emphasis: $neutral-800;           // textLowEmphasis: neutral800

// Brand Colors (using design system brand colors)
$brand-primary: $brand-red;                 // Primary brand color (Eneco red)
$brand-secondary: $brand-orange;            // Secondary brand color (Eneco orange)
$brand-functional: $accent-green-700;       // Functional brand color (accent green)

// Border Colors (borderColors from design system)
$border-divider-low: $neutral-300;          // borderDividerLowEmphasis: neutral300
$border-divider-medium: $neutral-400;       // borderDividerMediumEmphasis: neutral400
$border-divider-high: $neutral-900;         // borderDividerHighEmphasis: neutral900
$border-focus: $neutral-900;                // borderFocus: neutral900
$border-selected: $green-500;               // borderSelected: green500
$outline-hover: $neutral-300;               // outlineHover: neutral300

// Feedback Colors (feedbackColors from design system)
$feedback-error: $brand-dark-red;           // feedbackError: brandDarkRed
$feedback-success: $green-700;              // feedbackSuccess: green700
$feedback-warning: $orange-500;             // feedbackWarning: orange500 (using orange instead of yellow)
$feedback-info: $accent-green-600;          // feedbackInfo: accentGreen600

// Feedback Background Colors (feedbackColors from design system)
$feedback-bg-success: $green-100;           // feedbackBackgroundSuccess: green100
$feedback-bg-error: $neutral-100;           // feedbackBackgroundError: neutral100
$feedback-bg-warning: $orange-100;          // feedbackBackgroundWarning: orange100
$feedback-bg-info: $accent-green-100;       // feedbackBackgroundInfo: accentGreen100

// Link Colors (linkColors from design system)
$link-brand: $text-brand;                   // linkBrand: textBrand (accentGreen700)
$link-primary: $text-primary;               // linkPrimary: textPrimary (neutral900)
$link-secondary: $text-low-emphasis;        // linkSecondary: textLowEmphasis (neutral800)
$link-disabled: $neutral-300;               // linkDisabled: neutral300
$link-inverted: $text-inverted;             // linkInverted: textInverted (neutralWhite)

// Eneco Brand Colors (Legacy - mapped to actual design system brand colors)
$eneco-red-primary: $brand-red;             // #E5384C - Primary Eneco brand red
$eneco-red-secondary: $brand-orange;        // #EA714F - Secondary Eneco brand orange
$eneco-red-light: $brand-light-red;         // #F9C7CC - Light Eneco red
$eneco-green: $accent-green-700;            // #007250 - Eneco functional green (textBrand)
$eneco-orange: $brand-orange;               // #EA714F - Eneco orange
$eneco-white: $neutral-white;               // #FFF - White
$eneco-light-gray: $neutral-50;             // #FCFAFA - Light gray from design system

// Legacy purple mappings (for backward compatibility - now using red/orange)
$eneco-purple-primary: $eneco-red-primary;  // Map to red for consistency
$eneco-purple-secondary: $eneco-red-secondary; // Map to orange for consistency
$eneco-purple-light: $eneco-red-light;      // Map to light red for consistency

// Chat specific colors - Design System Aligned
$chat-user-bg: $brand-red;                  // User message background (brand red)
$chat-agent-bg: $background-tertiary;       // Agent message background (neutral300)
$chat-user-text: $text-inverted;            // User message text (white)
$chat-agent-text: $text-primary;            // Agent message text (neutral900)
$chat-header-bg: $brand-red;                // Chat header background (brand red)
$chat-accent: $accent-green-700;            // Chat accent color (functional green)

// Connection status colors (using design system feedback colors)
$status-connected: $feedback-success;       // Connected state (green700)
$status-connecting: $feedback-warning;      // Connecting state (orange500)
$status-disconnected: $neutral-400;         // Disconnected state (neutral400)
$status-error: $feedback-error;             // Error state (brandDarkRed)

// CSS Custom Properties - Design System Aligned
:root {
  // Light theme (default) - Design System Colors

  // Background Colors (backgroundColors from design system)
  --color-background: #{$eneco-light-gray};           // Main app background
  --color-background-primary: #{$background-primary}; // backgroundPrimary (neutralWhite)
  --color-background-secondary: #{$background-secondary}; // backgroundSecondary (neutral100)
  --color-background-tertiary: #{$background-tertiary}; // backgroundTertiary (neutral300)
  --color-background-scrim: #{$background-scrim};     // backgroundScrim (blackOpacity70)
  --color-background-dark: #{$background-dark};       // backgroundDark (neutral900)
  --color-background-pressed: #{$background-pressed}; // backgroundPressed (neutral100)

  // Surface Colors (legacy compatibility)
  --color-surface: #{$background-primary};            // Primary surface (white)
  --color-surface-secondary: #{$background-secondary}; // Secondary surface

  // Border Colors (borderColors from design system)
  --color-border: #{$border-divider-low};             // Default borders (neutral300)
  --color-border-hover: #{$outline-hover};            // Hover state borders (neutral300)
  --color-border-focus: #{$border-focus};             // Focus state borders (neutral900)
  --color-border-selected: #{$border-selected};       // Selected state borders (green500)

  // Text Colors (textColors from design system)
  --color-text-primary: #{$text-primary};             // textPrimary (neutral900)
  --color-text-secondary: #{$text-low-emphasis};      // textLowEmphasis (neutral800)
  --color-text-muted: #{$neutral-400};                // Medium emphasis text
  --color-text-brand: #{$text-brand};                 // textBrand (accentGreen700)
  --color-text-inverted: #{$text-inverted};           // textInverted (neutralWhite)
  --color-text-on-primary: #{$text-inverted};         // Text on primary background (white)

  // Brand Colors (using design system brand colors)
  --color-primary: #{$brand-primary};                 // Primary brand color (brandRed)
  --color-primary-hover: #{$brand-dark-red};          // Primary hover state (brandDarkRed)
  --color-primary-light: #{$brand-light-red};         // Light primary variant (brandLightRed)
  --color-secondary: #{$brand-secondary};             // Secondary brand color (brandOrange)
  --color-brand-functional: #{$brand-functional};     // Functional brand color (accentGreen700)

  // Feedback Colors (feedbackColors from design system)
  --color-success: #{$feedback-success};              // feedbackSuccess (green700)
  --color-error: #{$feedback-error};                  // feedbackError (brandDarkRed)
  --color-warning: #{$feedback-warning};              // feedbackWarning (orange500)
  --color-info: #{$feedback-info};                    // feedbackInfo (accentGreen600)

  // Feedback Background Colors (feedbackColors from design system)
  --color-feedback-bg-success: #{$feedback-bg-success}; // feedbackBackgroundSuccess (green100)
  --color-feedback-bg-error: #{$feedback-bg-error};   // feedbackBackgroundError (neutral100)
  --color-feedback-bg-warning: #{$feedback-bg-warning}; // feedbackBackgroundWarning (orange100)
  --color-feedback-bg-info: #{$feedback-bg-info};     // feedbackBackgroundInfo (accentGreen100)

  // Link Colors (linkColors from design system)
  --color-link-primary: #{$link-primary};             // linkPrimary (textPrimary)
  --color-link-brand: #{$link-brand};                 // linkBrand (textBrand - accentGreen700)
  --color-link-secondary: #{$link-secondary};         // linkSecondary (textLowEmphasis)
  --color-link-disabled: #{$link-disabled};           // linkDisabled (neutral300)
  --color-link-inverted: #{$link-inverted};           // linkInverted (textInverted)

  // Eneco brand colors (design system aligned)
  --color-eneco-red: #{$eneco-red-primary};           // Primary Eneco red (brandRed)
  --color-eneco-orange: #{$eneco-orange};             // Eneco orange (brandOrange)
  --color-eneco-green: #{$eneco-green};               // Eneco functional green (accentGreen700)
  --color-eneco-white: #{$eneco-white};               // Eneco white (neutralWhite)

  // Legacy purple mappings (for backward compatibility - now using red/orange)
  --color-eneco-purple: #{$eneco-red-primary};        // Map to red for consistency
  --color-eneco-purple-secondary: #{$eneco-red-secondary}; // Map to orange for consistency

  // Chat colors (design system aligned)
  --color-chat-user-bg: #{$chat-user-bg};             // User message background (brandRed)
  --color-chat-agent-bg: #{$chat-agent-bg};           // Agent message background (neutral300)
  --color-chat-user-text: #{$chat-user-text};         // User message text (neutralWhite)
  --color-chat-agent-text: #{$chat-agent-text};       // Agent message text (neutral900)
  --color-chat-header-bg: #{$chat-header-bg};         // Chat header background (brandRed)
  --color-chat-accent: #{$chat-accent};               // Chat accent color (accentGreen700)

  // Status colors (using design system feedback colors)
  --color-status-connected: #{$status-connected};     // Connected state (green700)
  --color-status-connecting: #{$status-connecting};   // Connecting state (orange500)
  --color-status-disconnected: #{$status-disconnected}; // Disconnected state (neutral400)
  --color-status-error: #{$status-error};             // Error state (brandDarkRed)
}

// Dark theme - Design System Aligned
[data-theme="dark"] {
  // Background Colors (dark theme variants using design system neutrals)
  --color-background: #{$neutral-900};                // Main app background (neutral900)
  --color-background-primary: #{$neutral-900};        // backgroundPrimary (dark - neutral900)
  --color-background-secondary: #{$neutral-800};      // backgroundSecondary (dark - neutral800)
  --color-background-tertiary: #{$neutral-400};       // backgroundTertiary (dark - neutral400)
  --color-background-scrim: #{$black-opacity-70};     // backgroundScrim (same as light)
  --color-background-dark: #{$neutral-black};         // backgroundDark (neutralBlack)
  --color-background-pressed: #{$neutral-800};        // backgroundPressed (dark - neutral800)

  // Surface Colors (dark theme)
  --color-surface: #{$neutral-900};                   // Primary surface (neutral900)
  --color-surface-secondary: #{$neutral-800};         // Secondary surface (neutral800)

  // Border Colors (dark theme)
  --color-border: #{$neutral-400};                    // Default borders (neutral400)
  --color-border-hover: #{$neutral-300};              // Hover state borders (neutral300)
  --color-border-focus: #{$neutral-white};            // Focus state borders (neutralWhite)
  --color-border-selected: #{$accent-green-600};      // Selected state borders (accentGreen600)

  // Text Colors (dark theme - using design system colors)
  --color-text-primary: #{$neutral-white};            // textPrimary (neutralWhite on dark)
  --color-text-secondary: #{$neutral-100};            // textSecondary (neutral100 on dark)
  --color-text-muted: #{$neutral-300};                // textMuted (neutral300 on dark)
  --color-text-brand: #{$accent-green-300};           // textBrand (lighter accentGreen for dark)
  --color-text-inverted: #{$neutral-900};             // textInverted (neutral900 on dark)
  --color-text-on-primary: #{$neutral-white};         // Text on primary background (white)

  // Brand Colors (dark theme - keep brand colors consistent)
  --color-primary: #{$brand-red};                     // Primary brand color (same brandRed)
  --color-primary-hover: #{$brand-dark-red};          // Primary hover state (brandDarkRed)
  --color-primary-light: #{$brand-light-red};         // Light primary variant (brandLightRed)
  --color-secondary: #{$brand-orange};                // Secondary brand color (brandOrange)
  --color-brand-functional: #{$accent-green-300};     // Functional brand color (lighter green for dark)

  // Feedback Colors (dark theme - keep consistent with brand)
  --color-success: #{$feedback-success};              // Success (green700 - same as light)
  --color-error: #{$feedback-error};                  // Error (brandDarkRed - same as light)
  --color-warning: #{$feedback-warning};              // Warning (orange500 - same as light)
  --color-info: #{$accent-green-300};                 // Info (lighter accentGreen for dark)

  // Feedback Background Colors (dark theme - darker backgrounds)
  --color-feedback-bg-success: #{$green-900};         // Success background (green900)
  --color-feedback-bg-error: #{$eneco-red-900};       // Error background (enecoRed900)
  --color-feedback-bg-warning: #{$neutral-800};       // Warning background (neutral800)
  --color-feedback-bg-info: #{$accent-green-800};     // Info background (accentGreen800)

  // Link Colors (dark theme)
  --color-link-primary: #{$neutral-white};            // linkPrimary (neutralWhite)
  --color-link-brand: #{$accent-green-300};           // linkBrand (lighter accentGreen)
  --color-link-secondary: #{$neutral-100};            // linkSecondary (neutral100)
  --color-link-disabled: #{$neutral-400};             // linkDisabled (neutral400)
  --color-link-inverted: #{$neutral-900};             // linkInverted (neutral900)

  // Chat colors (dark theme optimized)
  --color-chat-user-bg: #{$brand-red};                // User message (brandRed - same as light)
  --color-chat-agent-bg: #{$neutral-800};             // Agent message (neutral800)
  --color-chat-user-text: #{$neutral-white};          // User message text (neutralWhite)
  --color-chat-agent-text: #{$neutral-white};         // Agent message text (neutralWhite)
  --color-chat-header-bg: #{$brand-red};              // Chat header (brandRed - same as light)
  --color-chat-accent: #{$accent-green-300};          // Chat accent (lighter accentGreen)

  // Status colors (dark theme)
  --color-status-connected: #{$accent-green-300};     // Connected (lighter accentGreen)
  --color-status-connecting: #{$orange-300};          // Connecting (lighter orange)
  --color-status-disconnected: #{$neutral-400};       // Disconnected (neutral400)
  --color-status-error: #{$brand-red};                // Error (brandRed)
}
